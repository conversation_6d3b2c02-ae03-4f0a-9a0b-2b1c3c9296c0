import Cocoa

class AppDelegate: NSObject, NSApplicationDelegate {
    
    var window: NSWindow!
    
    func applicationDidFinishLaunching(_ aNotification: Notification) {
        
        // 设置基本菜单栏
        setupMenuBar()
        
        // 获取屏幕尺寸，创建与屏幕相等的窗口
        guard let screen = NSScreen.main else {
            fatalError("无法获取主屏幕")
        }
        let screenRect = screen.frame

        window = NSWindow(
            contentRect: screenRect,
            styleMask: [.titled, .closable, .miniaturizable, .resizable, .fullSizeContentView],
            backing: .buffered,
            defer: false
        )
        
        window.title = "太阳系模型"
        window.isOpaque = true
        window.backgroundColor = NSColor.black
        window.titlebarAppearsTransparent = true
        window.titleVisibility = .hidden

        // 创建视图控制器
        let viewController = ViewController()
        window.contentViewController = viewController

        // 设置窗口为全屏大小并显示
        window.setFrame(screen.frame, display: true)
        window.makeKeyAndOrderFront(nil)

        // 让窗口成为主窗口
        NSApp.activate(ignoringOtherApps: true)

        // 进入全屏模式
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.window.toggleFullScreen(nil)
        }
    }
    
    private func setupMenuBar() {
        let mainMenu = NSMenu()
        
        // 应用程序菜单
        let appMenuItem = NSMenuItem()
        mainMenu.addItem(appMenuItem)
        
        let appMenu = NSMenu()
        appMenuItem.submenu = appMenu
        
        // 关于菜单项
        let aboutMenuItem = NSMenuItem(title: "关于 太阳系模型", action: nil, keyEquivalent: "")
        appMenu.addItem(aboutMenuItem)
        
        appMenu.addItem(NSMenuItem.separator())
        
        // 退出菜单项
        let quitMenuItem = NSMenuItem(title: "退出", action: #selector(NSApplication.terminate(_:)), keyEquivalent: "q")
        appMenu.addItem(quitMenuItem)
        
        // 窗口菜单
        let windowMenuItem = NSMenuItem()
        mainMenu.addItem(windowMenuItem)
        
        let windowMenu = NSMenu(title: "窗口")
        windowMenuItem.submenu = windowMenu
        
        // 最小化菜单项
        let minimizeMenuItem = NSMenuItem(title: "最小化", action: #selector(NSWindow.miniaturize(_:)), keyEquivalent: "m")
        windowMenu.addItem(minimizeMenuItem)
        
        // 关闭菜单项
        let closeMenuItem = NSMenuItem(title: "关闭", action: #selector(NSWindow.performClose(_:)), keyEquivalent: "w")
        windowMenu.addItem(closeMenuItem)
        
        NSApp.mainMenu = mainMenu
    }
    
    func applicationWillTerminate(_ aNotification: Notification) {
        // 清理资源
    }
    
    func applicationShouldTerminateAfterLastWindowClosed(_ sender: NSApplication) -> Bool {
        return true
    }
}

// 应用程序入口点
let app = NSApplication.shared
let delegate = AppDelegate()
app.delegate = delegate
app.run()