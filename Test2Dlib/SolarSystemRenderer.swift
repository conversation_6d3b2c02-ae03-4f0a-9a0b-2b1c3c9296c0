//
//  SolarSystemRenderer.swift
//  Test2Dlib
//
//  Created by 王雷 on 4/6/25.
//

/*
 ==================================================================================
 ARCHITECTURE OVERVIEW - 太阳系渲染器架构概览
 ==================================================================================
 
 本程序采用分层统一架构，主要分为三个核心层：
 
 1. 【数据层】- 统一的数据结构定义
    - UnifiedVertex: 统一的3D顶点结构（位置+法向量）
    - Transform: 变换矩阵集合（模型+视图+投影）
    - SolarSystemParams: 太阳系物理参数配置
 
 2. 【渲染层】- 分离式双渲染管线
    - GeometryRenderer: 统一3D几何渲染器（处理球体、轨道等3D对象）
    - TextRenderer: 独立2D文字渲染器（处理季节文字显示）
 
 3. 【控制层】- 主渲染协调器
    - SolarSystemRenderer: 主渲染器，协调3D和2D渲染，管理动画系统
 
 ==================================================================================
 DESIGN PRINCIPLES - 设计原则
 ==================================================================================
 
 ✓ 统一性原则: 所有3D对象使用相同的顶点结构和着色器管线
 ✓ 分离原则: 3D几何渲染与2D文字渲染完全独立，互不干扰
 ✓ 可扩展性: 新增3D对象只需在GeometryRenderer中添加，无需修改其他组件
 ✓ 性能优化: 顶点数据预生成，变换矩阵统一计算，减少CPU-GPU数据传输
 
 ==================================================================================
 RENDERING PIPELINE - 渲染管线流程
 ==================================================================================
 
 1. 初始化阶段:
    初始化Metal设备 → 创建渲染器 → 生成几何数据 → 设置着色器管线
 
 2. 每帧渲染:
    更新动画时间 → 计算变换矩阵 → 渲染3D几何 → 渲染2D文字 → 提交GPU
 
 3. 3D渲染流程:
    设置统一管线状态 → 渲染轨道 → 渲染太阳 → 渲染地球 → 各自独立变换
 
 4. 2D渲染流程:
    计算季节文字 → 字体路径提取 → 三角剖分 → 顶点缓冲 → 独立渲染
 
 ==================================================================================
*/

import Foundation
import MetalKit
import CoreText
import LibTessSwift

// MARK: - 数据层：统一数据结构定义

/**
 * 统一顶点结构
 * 用途：所有3D几何对象（球体、轨道等）都使用此结构
 * 优势：统一的顶点格式使得可以使用同一套着色器管线
 */
struct UnifiedVertex {
    var position: SIMD3<Float>    // 3D世界坐标位置
    var normal: SIMD3<Float>      // 顶点法向量（用于光照计算）
}

/**
 * 变换矩阵集合
 * 用途：封装所有变换矩阵，传递给着色器
 * 包含：模型变换、视图变换、投影变换
 */
struct Transform {
    var modelMatrix: matrix_float4x4      // 模型到世界坐标变换
    var viewMatrix: matrix_float4x4       // 世界到摄像机坐标变换
    var projectionMatrix: matrix_float4x4 // 摄像机到屏幕坐标变换
}

/**
 * 太阳系物理参数配置
 * 用途：集中管理所有太阳系相关的物理常数
 * 设计：使用静态常量，便于调整和维护
 */
struct SolarSystemParams {
    static let sunRadius: Float = 0.02           // 太阳半径（相对单位）
    static let earthRadius: Float = 0.01         // 地球半径（相对单位）
    static let orbitRadius: Float = 0.3          // 地球轨道半径
    static let earthOrbitSpeed: Float = 1.0      // 地球公转速度（弧度/秒）
    static let seasonChangeAngle: Float = Float.pi / 2  // 季节变化角度（90度换一季）
}

// MARK: - 抗锯齿线条专用数据结构

/**
 * 抗锯齿线条顶点结构
 * 用途：专门为高质量线条渲染设计，支持厚度和抗锯齿
 */
struct AntiAliasedLineVertex {
    var position: SIMD3<Float>    // 3D世界坐标位置
    var direction: SIMD2<Float>   // 线条方向（用于抗锯齿计算）
    var offset: Float             // 垂直于线条的偏移（厚度控制）
}

/**
 * 线条渲染统一参数
 * 用途：控制线条的视觉效果和抗锯齿质量
 */
struct LineUniforms {
    var lineWidth: Float         // 线条宽度（像素）
    var screenWidth: Float       // 屏幕宽度
    var screenHeight: Float      // 屏幕高度
    var antialiasingWidth: Float // 抗锯齿边缘宽度
}

// MARK: - 渲染层：几何渲染器（统一3D管线）

/**
 * 统一3D几何渲染器
 * 
 * 架构特点：
 * - 统一管线：所有3D对象（太阳、地球、轨道）使用相同的顶点结构和着色器
 * - 高效渲染：顶点数据预生成，只在渲染时传递变换矩阵
 * - 模块化设计：每种几何对象有独立的渲染方法，便于维护和扩展
 * 
 * 渲染对象：
 * 1. 球体几何：太阳和地球使用相同的球体网格，通过变换矩阵区分大小和位置
 * 2. 轨道几何：圆形轨道路径，使用线段渲染模式
 */
class GeometryRenderer {
    // Metal核心组件
    private let device: MTLDevice
    private var pipelineState: MTLRenderPipelineState?
    
    // 抗锯齿线条渲染管线
    private var antiAliasedLinePipelineState: MTLRenderPipelineState?
    private var lineUniformsBuffer: MTLBuffer?
    
    // 几何数据缓冲区（预生成，复用）
    private var sphereVertexBuffer: MTLBuffer?    // 球体顶点缓冲区
    private var sphereIndexBuffer: MTLBuffer?     // 球体索引缓冲区
    private var orbitVertexBuffer: MTLBuffer?     // 轨道顶点缓冲区
    private var sphereIndexCount: Int = 0         // 球体索引数量
    private var orbitVertexCount: Int = 0         // 轨道顶点数量
    
    // 抗锯齿轨道渲染数据
    private var antiAliasedOrbitVertexBuffer: MTLBuffer?
    private var antiAliasedOrbitIndexBuffer: MTLBuffer?
    private var antiAliasedOrbitIndexCount: Int = 0
    
    init(device: MTLDevice, view: MTKView) {
        self.device = device
        setupPipeline(view: view)              // 设置统一渲染管线
        setupAntiAliasedLinePipeline(view: view) // 设置抗锯齿线条管线
        setupGeometry()                       // 预生成几何数据
        setupLineUniforms(view: view)         // 设置线条渲染参数
    }
    
    /**
     * 设置统一3D渲染管线
     * 特点：一套管线处理所有3D几何对象，提高渲染效率
     */
    private func setupPipeline(view: MTKView) {
        guard let library = device.makeDefaultLibrary() else {
            fatalError("无法创建 Metal 库")
        }
        
        // 使用统一的3D着色器函数
        guard let vertexFunction = library.makeFunction(name: "unified_vertex"),
              let fragmentFunction = library.makeFunction(name: "unified_fragment") else {
            fatalError("无法创建着色器函数")
        }
        
        // 配置统一的3D顶点描述符
        let vertexDescriptor = MTLVertexDescriptor()
        
        // 位置属性 (attribute 0) - 3D位置
        vertexDescriptor.attributes[0].format = .float3
        vertexDescriptor.attributes[0].offset = 0
        vertexDescriptor.attributes[0].bufferIndex = 0
        
        // 法向量属性 (attribute 1) - 3D法向量（为未来光照扩展准备）
        vertexDescriptor.attributes[1].format = .float3
        vertexDescriptor.attributes[1].offset = MemoryLayout<SIMD3<Float>>.size
        vertexDescriptor.attributes[1].bufferIndex = 0
        
        // 顶点缓冲区布局
        vertexDescriptor.layouts[0].stride = MemoryLayout<UnifiedVertex>.stride
        vertexDescriptor.layouts[0].stepRate = 1
        vertexDescriptor.layouts[0].stepFunction = .perVertex
        
        let pipelineDescriptor = MTLRenderPipelineDescriptor()
        pipelineDescriptor.vertexFunction = vertexFunction
        pipelineDescriptor.fragmentFunction = fragmentFunction
        pipelineDescriptor.vertexDescriptor = vertexDescriptor
        pipelineDescriptor.colorAttachments[0].pixelFormat = view.colorPixelFormat
        pipelineDescriptor.depthAttachmentPixelFormat = view.depthStencilPixelFormat
        pipelineDescriptor.sampleCount = view.sampleCount
        
        do {
            pipelineState = try device.makeRenderPipelineState(descriptor: pipelineDescriptor)
        } catch {
            fatalError("创建统一渲染管线失败: \(error)")
        }
    }
    
    /**
     * 设置抗锯齿线条渲染管线
     * 特点：专门为高质量线条渲染优化，支持厚度控制和平滑抗锯齿
     */
    private func setupAntiAliasedLinePipeline(view: MTKView) {
        guard let library = device.makeDefaultLibrary() else {
            fatalError("无法创建 Metal 库")
        }
        
        // 使用专门的抗锯齿线条着色器
        guard let vertexFunction = library.makeFunction(name: "orbitLineVertex"),
              let fragmentFunction = library.makeFunction(name: "orbitLineFragment") else {
            fatalError("无法创建抗锯齿线条着色器函数")
        }
        
        // 抗锯齿线条顶点描述符
        let vertexDescriptor = MTLVertexDescriptor()
        
        // 位置属性 (attribute 0) - 3D位置
        vertexDescriptor.attributes[0].format = .float3
        vertexDescriptor.attributes[0].offset = 0
        vertexDescriptor.attributes[0].bufferIndex = 0
        
        // 方向属性 (attribute 1) - 2D方向向量
        vertexDescriptor.attributes[1].format = .float2
        vertexDescriptor.attributes[1].offset = MemoryLayout<SIMD3<Float>>.size
        vertexDescriptor.attributes[1].bufferIndex = 0
        
        // 偏移属性 (attribute 2) - 厚度偏移
        vertexDescriptor.attributes[2].format = .float
        vertexDescriptor.attributes[2].offset = MemoryLayout<SIMD3<Float>>.size + MemoryLayout<SIMD2<Float>>.size
        vertexDescriptor.attributes[2].bufferIndex = 0
        
        // 顶点缓冲区布局
        vertexDescriptor.layouts[0].stride = MemoryLayout<AntiAliasedLineVertex>.stride
        vertexDescriptor.layouts[0].stepRate = 1
        vertexDescriptor.layouts[0].stepFunction = .perVertex
        
        let pipelineDescriptor = MTLRenderPipelineDescriptor()
        pipelineDescriptor.vertexFunction = vertexFunction
        pipelineDescriptor.fragmentFunction = fragmentFunction
        pipelineDescriptor.vertexDescriptor = vertexDescriptor
        pipelineDescriptor.colorAttachments[0].pixelFormat = view.colorPixelFormat
        pipelineDescriptor.depthAttachmentPixelFormat = view.depthStencilPixelFormat
        pipelineDescriptor.sampleCount = view.sampleCount
        
        // 启用混合（支持抗锯齿透明度）
        pipelineDescriptor.colorAttachments[0].isBlendingEnabled = true
        pipelineDescriptor.colorAttachments[0].rgbBlendOperation = .add
        pipelineDescriptor.colorAttachments[0].alphaBlendOperation = .add
        pipelineDescriptor.colorAttachments[0].sourceRGBBlendFactor = .sourceAlpha
        pipelineDescriptor.colorAttachments[0].sourceAlphaBlendFactor = .one
        pipelineDescriptor.colorAttachments[0].destinationRGBBlendFactor = .oneMinusSourceAlpha
        pipelineDescriptor.colorAttachments[0].destinationAlphaBlendFactor = .one
        
        do {
            antiAliasedLinePipelineState = try device.makeRenderPipelineState(descriptor: pipelineDescriptor)
        } catch {
            fatalError("创建抗锯齿线条渲染管线失败: \(error)")
        }
    }
    
    /**
     * 设置线条渲染统一参数
     * 配置线条宽度、屏幕尺寸和抗锯齿参数
     */
    private func setupLineUniforms(view: MTKView) {
        var uniforms = LineUniforms(
            lineWidth: 2.0,              // 线条宽度（像素）
            screenWidth: Float(view.drawableSize.width),
            screenHeight: Float(view.drawableSize.height),
            antialiasingWidth: 1.0       // 抗锯齿边缘宽度
        )
        
        lineUniformsBuffer = device.makeBuffer(bytes: &uniforms,
                                             length: MemoryLayout<LineUniforms>.stride,
                                             options: [.storageModeShared])
    }

    /**
     * 预生成几何数据
     * 策略：几何数据在初始化时生成一次，渲染时复用，提高性能
     */
    private func setupGeometry() {
        // 创建3D球体几何（太阳和地球共用）
        let (sphereVertices, sphereIndices) = generateSphere3D(radius: 1.0, segments: 32)
        sphereVertexBuffer = device.makeBuffer(bytes: sphereVertices,
                                             length: sphereVertices.count * MemoryLayout<UnifiedVertex>.stride,
                                             options: [])
        sphereIndexBuffer = device.makeBuffer(bytes: sphereIndices,
                                            length: sphereIndices.count * MemoryLayout<UInt16>.stride,
                                            options: [])
        sphereIndexCount = sphereIndices.count
        
        // 创建轨道几何
        let orbitVertices = generateOrbit3D(segments: 64)
        orbitVertexBuffer = device.makeBuffer(bytes: orbitVertices,
                                            length: orbitVertices.count * MemoryLayout<UnifiedVertex>.stride,
                                            options: [])
        orbitVertexCount = orbitVertices.count
        
        // 创建抗锯齿轨道几何
        let (antiAliasedOrbitVertices, antiAliasedOrbitIndices) = generateAntiAliasedOrbit3D(segments: 128)
        antiAliasedOrbitVertexBuffer = device.makeBuffer(bytes: antiAliasedOrbitVertices,
                                                       length: antiAliasedOrbitVertices.count * MemoryLayout<AntiAliasedLineVertex>.stride,
                                                       options: [])
        antiAliasedOrbitIndexBuffer = device.makeBuffer(bytes: antiAliasedOrbitIndices,
                                                      length: antiAliasedOrbitIndices.count * MemoryLayout<UInt16>.stride,
                                                      options: [])
        antiAliasedOrbitIndexCount = antiAliasedOrbitIndices.count
    }
    
    /**
     * 生成3D球体几何
     * 算法：球坐标系生成，包含位置和法向量
     * 参数：radius - 半径，segments - 分段数（影响球体精度）
     */
    private func generateSphere3D(radius: Float, segments: Int) -> ([UnifiedVertex], [UInt16]) {
        var vertices: [UnifiedVertex] = []
        var indices: [UInt16] = []
        
        // 生成球面顶点（球坐标转换为笛卡尔坐标）
        for i in 0...segments {
            let lat = Float(i) / Float(segments) * Float.pi - Float.pi / 2  // 纬度：-π/2 到 π/2
            for j in 0...segments {
                let lon = Float(j) / Float(segments) * 2 * Float.pi        // 经度：0 到 2π
                
                let x = cos(lat) * cos(lon) * radius
                let y = sin(lat) * radius
                let z = cos(lat) * sin(lon) * radius
                
                let position = SIMD3<Float>(x, y, z)
                let normal = normalize(position)  // 球面法向量 = 标准化位置向量
                
                vertices.append(UnifiedVertex(position: position, normal: normal))
            }
        }
        
        // 生成三角形索引
        for i in 0..<segments {
            for j in 0..<segments {
                let current = UInt16(i * (segments + 1) + j)
                let next = UInt16(i * (segments + 1) + j + 1)
                let below = UInt16((i + 1) * (segments + 1) + j)
                let belowNext = UInt16((i + 1) * (segments + 1) + j + 1)
                
                // 两个三角形组成一个四边形
                indices.append(contentsOf: [current, below, next])
                indices.append(contentsOf: [next, below, belowNext])
            }
        }
        
        return (vertices, indices)
    }
    
    /**
     * 生成3D轨道几何
     * 算法：圆周上均匀分布点，Y坐标固定为0（水平轨道）
     */
    private func generateOrbit3D(segments: Int) -> [UnifiedVertex] {
        var vertices: [UnifiedVertex] = []
        
        for i in 0...segments {  // 包含首尾连接点
            let angle = Float(i) / Float(segments) * 2 * Float.pi
            let x = cos(angle)
            let z = sin(angle)
            let position = SIMD3<Float>(x, 0, z)  // 单位圆，Y=0
            let normal = SIMD3<Float>(0, 1, 0)    // 向上法向量
            
            vertices.append(UnifiedVertex(position: position, normal: normal))
        }
        
        return vertices
    }
    
    /**
     * 生成抗锯齿3D轨道几何
     * 算法：为每个线段生成带厚度的四边形，支持高质量抗锯齿渲染
     * 参数：segments - 分段数（更多分段 = 更平滑的圆形）
     * 返回：(顶点数组, 索引数组) - 用于三角形渲染的网格数据
     */
    private func generateAntiAliasedOrbit3D(segments: Int) -> ([AntiAliasedLineVertex], [UInt16]) {
        var vertices: [AntiAliasedLineVertex] = []
        var indices: [UInt16] = []
        
        // 为每个线段生成四个顶点（形成带厚度的四边形）
        for i in 0..<segments {
            let currentAngle = Float(i) / Float(segments) * 2 * Float.pi
            let nextAngle = Float(i + 1) / Float(segments) * 2 * Float.pi
            
            // 当前点和下一个点的位置
            let currentPos = SIMD3<Float>(cos(currentAngle), 0, sin(currentAngle))
            let nextPos = SIMD3<Float>(cos(nextAngle), 0, sin(nextAngle))
            
            // 计算线段方向（用于抗锯齿计算）
            let direction = normalize(SIMD2<Float>(nextPos.x - currentPos.x, nextPos.z - currentPos.z))
            
            // 为每个线段创建四个顶点（上下两条边）
            let baseIndex = UInt16(i * 4)
            
            // 顶点0：当前点，上边缘
            vertices.append(AntiAliasedLineVertex(
                position: currentPos,
                direction: direction,
                offset: 1.0  // 正偏移（线条上边缘）
            ))
            
            // 顶点1：当前点，下边缘
            vertices.append(AntiAliasedLineVertex(
                position: currentPos,
                direction: direction,
                offset: -1.0  // 负偏移（线条下边缘）
            ))
            
            // 顶点2：下一个点，上边缘
            vertices.append(AntiAliasedLineVertex(
                position: nextPos,
                direction: direction,
                offset: 1.0
            ))
            
            // 顶点3：下一个点，下边缘
            vertices.append(AntiAliasedLineVertex(
                position: nextPos,
                direction: direction,
                offset: -1.0
            ))
            
            // 为这个线段创建两个三角形（组成四边形）
            // 三角形1：0-1-2
            indices.append(contentsOf: [baseIndex, baseIndex + 1, baseIndex + 2])
            // 三角形2：1-3-2
            indices.append(contentsOf: [baseIndex + 1, baseIndex + 3, baseIndex + 2])
        }
        
        return (vertices, indices)
    }
    
    /**
     * 统一渲染入口
     * 渲染顺序：轨道 → 太阳 → 地球
     * 设计理念：每个对象独立渲染，通过变换矩阵控制位置和大小
     */
    func render(encoder: MTLRenderCommandEncoder, 
               transformBuffer: MTLBuffer?,
               solarSystemTransform: matrix_float4x4,
               animationTime: Double) {
        
        guard let pipelineState = pipelineState else { return }
        
        // 渲染抗锯齿轨道（使用专门的高质量管线）
        renderAntiAliasedOrbit(encoder: encoder, globalTransform: solarSystemTransform)
        
        // 设置统一渲染管线状态（用于球体）
        encoder.setRenderPipelineState(pipelineState)
        encoder.setVertexBuffer(transformBuffer, offset: 0, index: 1)
        
        // 渲染太阳（中心静止对象）
        renderSun(encoder: encoder, globalTransform: solarSystemTransform)
        
        // 渲染地球（动态轨道对象）
        renderEarth(encoder: encoder, globalTransform: solarSystemTransform, animationTime: animationTime)
    }
    
    /**
     * 渲染轨道
     * 变换策略：全局变换 × 轨道缩放变换
     */
    private func renderOrbit(encoder: MTLRenderCommandEncoder, globalTransform: matrix_float4x4) {
        guard let orbitBuffer = orbitVertexBuffer else { return }
        
        encoder.setVertexBuffer(orbitBuffer, offset: 0, index: 0)
        
        // 轨道变换：单位圆缩放到轨道大小
        let orbitScale = scaleMatrix(SIMD3<Float>(SolarSystemParams.orbitRadius,
                                                 SolarSystemParams.orbitRadius,
                                                 SolarSystemParams.orbitRadius))
        var orbitTransform = globalTransform * orbitScale
        encoder.setVertexBytes(&orbitTransform, length: MemoryLayout<matrix_float4x4>.stride, index: 2)
        
        // 设置黑色
        var color = SIMD4<Float>(0.0, 0.0, 0.0, 1.0)
        encoder.setFragmentBytes(&color, length: MemoryLayout<SIMD4<Float>>.stride, index: 0)
        
        encoder.drawPrimitives(type: .lineStrip, vertexStart: 0, vertexCount: orbitVertexCount)
    }
    
    /**
     * 渲染抗锯齿轨道
     * 使用专门的抗锯齿线条管线，提供高质量的线条渲染效果
     * 特点：支持厚度控制、平滑边缘、gamma校正等高级抗锯齿技术
     */
    private func renderAntiAliasedOrbit(encoder: MTLRenderCommandEncoder, globalTransform: matrix_float4x4) {
        guard let pipelineState = antiAliasedLinePipelineState,
              let vertexBuffer = antiAliasedOrbitVertexBuffer,
              let indexBuffer = antiAliasedOrbitIndexBuffer,
              let uniformsBuffer = lineUniformsBuffer else { return }
        
        // 设置抗锯齿线条渲染管线
        encoder.setRenderPipelineState(pipelineState)
        encoder.setVertexBuffer(vertexBuffer, offset: 0, index: 0)
        encoder.setVertexBuffer(uniformsBuffer, offset: 0, index: 1)
        
        // 轨道变换：单位圆缩放到轨道大小
        let orbitScale = scaleMatrix(SIMD3<Float>(SolarSystemParams.orbitRadius,
                                                 SolarSystemParams.orbitRadius,
                                                 SolarSystemParams.orbitRadius))
        var orbitTransform = globalTransform * orbitScale
        encoder.setVertexBytes(&orbitTransform, length: MemoryLayout<matrix_float4x4>.stride, index: 2)
        
        // 设置线条颜色（深灰色，比球体稍淡）
        var color = SIMD4<Float>(0.3, 0.3, 0.3, 1.0)
        encoder.setFragmentBytes(&color, length: MemoryLayout<SIMD4<Float>>.stride, index: 0)
        
        encoder.drawIndexedPrimitives(type: .triangle,
                                    indexCount: antiAliasedOrbitIndexCount,
                                    indexType: .uint16,
                                    indexBuffer: indexBuffer,
                                    indexBufferOffset: 0)
    }
    
    /**
     * 渲染太阳
     * 变换策略：全局变换 × 太阳缩放变换
     * 位置：固定在坐标原点（太阳系中心）
     */
    private func renderSun(encoder: MTLRenderCommandEncoder, globalTransform: matrix_float4x4) {
        guard let vertexBuffer = sphereVertexBuffer,
              let indexBuffer = sphereIndexBuffer else { return }
        
        encoder.setVertexBuffer(vertexBuffer, offset: 0, index: 0)
        
        // 太阳变换：缩放到太阳大小
        let sunScale = scaleMatrix(SIMD3<Float>(SolarSystemParams.sunRadius,
                                              SolarSystemParams.sunRadius,
                                              SolarSystemParams.sunRadius))
        var sunTransform = globalTransform * sunScale
        encoder.setVertexBytes(&sunTransform, length: MemoryLayout<matrix_float4x4>.stride, index: 2)
        
        // 设置黑色（简化显示，可扩展为黄色）
        var color = SIMD4<Float>(0.0, 0.0, 0.0, 1.0)
        encoder.setFragmentBytes(&color, length: MemoryLayout<SIMD4<Float>>.stride, index: 0)
        
        encoder.drawIndexedPrimitives(type: .triangle,
                                    indexCount: sphereIndexCount,
                                    indexType: .uint16,
                                    indexBuffer: indexBuffer,
                                    indexBufferOffset: 0)
    }
    
    /**
     * 渲染地球
     * 变换策略：全局变换 × 轨道位置变换 × 地球缩放变换
     * 动画：根据动画时间计算轨道位置
     */
    private func renderEarth(encoder: MTLRenderCommandEncoder, globalTransform: matrix_float4x4, animationTime: Double) {
        guard let vertexBuffer = sphereVertexBuffer,
              let indexBuffer = sphereIndexBuffer else { return }
        
        encoder.setVertexBuffer(vertexBuffer, offset: 0, index: 0)
        
        // 地球变换：缩放 -> 轨道位置 -> 全局变换
        let earthScale = scaleMatrix(SIMD3<Float>(SolarSystemParams.earthRadius,
                                                SolarSystemParams.earthRadius,
                                                SolarSystemParams.earthRadius))
        
        // 地球轨道位置（使用动画时间计算圆周运动）
        let angle = Float(animationTime)
        let earthX = SolarSystemParams.orbitRadius * cos(angle)
        let earthY = SolarSystemParams.orbitRadius * sin(angle)
        let earthOrbitTranslation = translationMatrix(SIMD3<Float>(earthX, earthY, 0.0))
        
        var earthTransform = globalTransform * earthOrbitTranslation * earthScale
        encoder.setVertexBytes(&earthTransform, length: MemoryLayout<matrix_float4x4>.stride, index: 2)
        
        // 设置黑色（简化显示，可扩展为蓝色）
        var color = SIMD4<Float>(0.0, 0.0, 0.0, 1.0)
        encoder.setFragmentBytes(&color, length: MemoryLayout<SIMD4<Float>>.stride, index: 0)
        
        encoder.drawIndexedPrimitives(type: .triangle,
                                    indexCount: sphereIndexCount,
                                    indexType: .uint16,
                                    indexBuffer: indexBuffer,
                                    indexBufferOffset: 0)
    }
    
    // MARK: - 数学辅助函数
    
    /**
     * 生成平移变换矩阵
     */
    private func translationMatrix(_ translation: SIMD3<Float>) -> matrix_float4x4 {
        return matrix_float4x4(columns: (
            SIMD4<Float>(1, 0, 0, 0),
            SIMD4<Float>(0, 1, 0, 0),
            SIMD4<Float>(0, 0, 1, 0),
            SIMD4<Float>(translation.x, translation.y, translation.z, 1)
        ))
    }
    
    /**
     * 生成缩放变换矩阵
     */
    private func scaleMatrix(_ scale: SIMD3<Float>) -> matrix_float4x4 {
        return matrix_float4x4(columns: (
            SIMD4<Float>(scale.x, 0, 0, 0),
            SIMD4<Float>(0, scale.y, 0, 0),
            SIMD4<Float>(0, 0, scale.z, 0),
            SIMD4<Float>(0, 0, 0, 1)
        ))
    }
}

// MARK: - 渲染层：文字渲染器（独立2D文字层）

/**
 * 独立2D文字渲染器
 * 
 * 架构特点：
 * - 完全独立：与3D几何渲染完全分离，使用不同的着色器和管线
 * - 高级文字处理：支持中文字体，使用CoreText进行字体路径提取
 * - 三角剖分：将文字路径转换为GPU可渲染的三角形网格
 * - 动态内容：根据动画时间动态显示不同季节文字
 * 
 * 技术栈：
 * 1. CoreText：字体和文字布局处理
 * 2. LibTessSwift：贝塞尔曲线三角剖分
 * 3. Metal：2D图形渲染管线
 */
class TextRenderer {
    // Metal核心组件
    private let device: MTLDevice
    private var pipelineState: MTLRenderPipelineState?
    
    // 文字处理组件
    private var tessellator: TessC?                    // 三角剖分器
    private let seasons = ["春", "夏", "秋", "冬"]      // 季节文字数组
    private var currentSeason: Int = 0                 // 当前季节索引
    
    init(device: MTLDevice, view: MTKView) {
        self.device = device
        setupPipeline(view: view)    // 设置2D文字渲染管线
        setupTessellator()          // 初始化三角剖分器
    }
    
    /**
     * 设置2D文字渲染管线
     * 特点：专门为2D文字优化，与3D管线完全独立
     */
    private func setupPipeline(view: MTKView) {
        guard let library = device.makeDefaultLibrary() else {
            fatalError("无法创建 Metal 库")
        }
        
        // 使用专门的2D文字着色器
        guard let vertexFunction = library.makeFunction(name: "text_vertex"),
              let fragmentFunction = library.makeFunction(name: "text_fragment") else {
            fatalError("无法创建文字着色器函数")
        }
        
        // 2D文字顶点描述符（只需要2D位置）
        let vertexDescriptor = MTLVertexDescriptor()
        
        // 位置属性 - 2D位置
        vertexDescriptor.attributes[0].format = .float2
        vertexDescriptor.attributes[0].offset = 0
        vertexDescriptor.attributes[0].bufferIndex = 0
        
        vertexDescriptor.layouts[0].stride = MemoryLayout<SIMD2<Float>>.size
        vertexDescriptor.layouts[0].stepRate = 1
        vertexDescriptor.layouts[0].stepFunction = .perVertex
        
        let pipelineDescriptor = MTLRenderPipelineDescriptor()
        pipelineDescriptor.vertexFunction = vertexFunction
        pipelineDescriptor.fragmentFunction = fragmentFunction
        pipelineDescriptor.vertexDescriptor = vertexDescriptor
        pipelineDescriptor.colorAttachments[0].pixelFormat = view.colorPixelFormat
        pipelineDescriptor.depthAttachmentPixelFormat = view.depthStencilPixelFormat
        pipelineDescriptor.sampleCount = view.sampleCount
        
        // 启用混合（支持文字透明度）
        pipelineDescriptor.colorAttachments[0].isBlendingEnabled = true
        pipelineDescriptor.colorAttachments[0].rgbBlendOperation = .add
        pipelineDescriptor.colorAttachments[0].alphaBlendOperation = .add
        pipelineDescriptor.colorAttachments[0].sourceRGBBlendFactor = .sourceAlpha
        pipelineDescriptor.colorAttachments[0].sourceAlphaBlendFactor = .sourceAlpha
        pipelineDescriptor.colorAttachments[0].destinationRGBBlendFactor = .oneMinusSourceAlpha
        pipelineDescriptor.colorAttachments[0].destinationAlphaBlendFactor = .oneMinusSourceAlpha
        
        do {
            pipelineState = try device.makeRenderPipelineState(descriptor: pipelineDescriptor)
        } catch {
            fatalError("创建文字渲染管线失败: \(error)")
        }
    }
    
    /**
     * 初始化三角剖分器
     * 用途：将复杂的文字路径（包含曲线）转换为GPU可渲染的三角形
     */
    private func setupTessellator() {
        tessellator = TessC()
        guard tessellator != nil else {
            fatalError("无法初始化 LibTessSwift")
        }
    }
    
    /**
     * 文字渲染主入口
     * 流程：计算季节 → 生成文字几何 → 创建顶点缓冲 → 渲染
     */
    func render(encoder: MTLRenderCommandEncoder,
                transformBuffer: MTLBuffer?,
                animationTime: Double,
                aspectRatio: Float) {
        guard let pipelineState = pipelineState else { return }
        
        // 根据动画时间计算当前季节
        let seasonIndex = Int(animationTime / Double(SolarSystemParams.seasonChangeAngle)) % 4
        currentSeason = seasonIndex
        let seasonText = seasons[seasonIndex]
        
        // 动态生成文字几何（每帧重新计算，支持动态文字）
        let textOffset = SIMD2<Float>(aspectRatio - 0.3, 0.0)  // 屏幕右侧位置
        guard let textVertices = generateTextVertices(text: seasonText, 
                                                    size: 0.8 * 6.0, 
                                                    offset: textOffset,
                                                    aspectRatio: aspectRatio) else { return }
        
        guard !textVertices.isEmpty else { return }
        
        // 创建临时顶点缓冲（文字内容可变，每帧重新创建）
        guard let textBuffer = device.makeBuffer(bytes: textVertices,
                                               length: textVertices.count * MemoryLayout<SIMD2<Float>>.stride,
                                               options: []) else { return }
        
        // 设置2D文字渲染状态
        encoder.setRenderPipelineState(pipelineState)
        encoder.setVertexBuffer(textBuffer, offset: 0, index: 0)
        encoder.setVertexBuffer(transformBuffer, offset: 0, index: 1)
        
        // 文字使用单位矩阵（位置已在顶点中计算）
        var textTransform = matrix_identity_float4x4
        encoder.setVertexBytes(&textTransform, length: MemoryLayout<matrix_float4x4>.stride, index: 2)
        
        // 设置黑色文字
        var color = SIMD4<Float>(0.0, 0.0, 0.0, 1.0)
        encoder.setFragmentBytes(&color, length: MemoryLayout<SIMD4<Float>>.stride, index: 0)
        
        encoder.drawPrimitives(type: .triangle, vertexStart: 0, vertexCount: textVertices.count)
    }
    
    /**
     * 生成文字顶点数据
     * 核心流程：文字 → CoreText路径 → 贝塞尔曲线采样 → 三角剖分 → 顶点数组
     */
    private func generateTextVertices(text: String, 
                                    size: Float, 
                                    offset: SIMD2<Float>,
                                    aspectRatio: Float) -> [SIMD2<Float>]? {
        guard let tessellator = tessellator else { return nil }
        
        var vertices: [SIMD2<Float>] = []
        
        // 1. 创建文字属性和布局
        let fontSize = CGFloat(144.0)  // 高分辨率字体大小
        let font = NSFont(name: "PingFang SC", size: fontSize) ?? NSFont.systemFont(ofSize: fontSize)
        
        // 定义路径处理上下文类
        class PathProcessingContext {
            var contourVertices: [SIMD2<Float>] = []    // 当前轮廓顶点
            var allContours: [[SIMD2<Float>]] = []      // 所有轮廓集合
            let offset: SIMD2<Float>
            let scale: Float
            
            init(offset: SIMD2<Float>, scale: Float) {
                self.offset = offset
                self.scale = scale
            }
        }
        
        let context = PathProcessingContext(offset: offset, scale: scale)
        
        // 创建字体路径
        let attributedString = NSAttributedString(string: text, attributes: [.font: font])
        let line = CTLineCreateWithAttributedString(attributedString)
        guard let glyphRuns = CTLineGetGlyphRuns(line) as? [CTRun], !glyphRuns.isEmpty else {
            return nil
        }
        
        // 遍历所有字形
        for run in glyphRuns {
            let glyphCount = CTRunGetGlyphCount(run)
            var glyphs = Array<CGGlyph>(repeating: 0, count: glyphCount)
            CTRunGetGlyphs(run, CFRange(location: 0, length: glyphCount), &glyphs)
            
            guard let runFont = CTRunGetAttributes(run)[kCTFontAttributeName] as? CTFont else { continue }
            
            for glyph in glyphs {
                guard let path = CTFontCreatePathForGlyph(runFont, glyph, nil) else { continue }
                
                // 解析路径元素
                path.apply(info: Unmanaged.passUnretained(context).toOpaque()) { contextPtr, element in
                    guard let contextPtr = contextPtr else { return }
                    let context = Unmanaged<PathProcessingContext>.fromOpaque(contextPtr).takeUnretainedValue()
                    let points = element.pointee.points
                    
                    switch element.pointee.type {
                    case .moveToPoint:
                        // 开始新轮廓
                        if !context.contourVertices.isEmpty {
                            context.allContours.append(context.contourVertices)
                            context.contourVertices.removeAll()
                        }
                }
                let point = transformPathPoint(points[0], offset: context.offset, scale: context.scale)
                context.contourVertices.append(point)
                
            case .addLineToPoint:
                // 直线段
                let point = transformPathPoint(points[0], offset: context.offset, scale: context.scale)
                context.contourVertices.append(point)
                
            case .addQuadCurveToPoint:
                // 二次贝塞尔曲线采样
                let currentPointSIMD = context.contourVertices.last
                if let currentSIMD = currentPointSIMD {
                    let originalX = (currentSIMD.x - context.offset.x) / context.scale
                    let originalY = (currentSIMD.y - context.offset.y) / context.scale
                    let currentPoint = CGPoint(x: CGFloat(originalX), y: CGFloat(originalY))
                    
                    let controlPoint = points[0]
                    let endPoint = points[1]
                    let curveSegments = 20  // 曲线细分段数
                    let sampledPoints = sampleQuadraticCurve(p0: currentPoint, p1: controlPoint, p2: endPoint, segments: curveSegments)
                    
                    for i in 1..<sampledPoints.count {
                        let point = transformPathPoint(sampledPoints[i], offset: context.offset, scale: context.scale)
                        context.contourVertices.append(point)
                    }
                } else {
                    let point = transformPathPoint(points[1], offset: context.offset, scale: context.scale)
                    context.contourVertices.append(point)
                }
                
            case .addCurveToPoint:
                // 三次贝塞尔曲线采样
                let currentPointSIMD = context.contourVertices.last
                if let currentSIMD = currentPointSIMD {
                    let originalX = (currentSIMD.x - context.offset.x) / context.scale
                    let originalY = (currentSIMD.y - context.offset.y) / context.scale
                    let currentPoint = CGPoint(x: CGFloat(originalX), y: CGFloat(originalY))
                    
                    let controlPoint1 = points[0]
                    let controlPoint2 = points[1]
                    let endPoint = points[2]
                    let curveSegments = 20  // 曲线细分段数
                    let sampledPoints = sampleCubicCurve(p0: currentPoint, p1: controlPoint1, p2: controlPoint2, p3: endPoint, segments: curveSegments)
                    
                    for i in 1..<sampledPoints.count {
                        let point = transformPathPoint(sampledPoints[i], offset: context.offset, scale: context.scale)
                        context.contourVertices.append(point)
                    }
                } else {
                    let point = transformPathPoint(points[2], offset: context.offset, scale: context.scale)
                    context.contourVertices.append(point)
                }
                
            case .closeSubpath:
                // 闭合轮廓
                if let first = context.contourVertices.first, context.contourVertices.count > 2 {
                    context.contourVertices.append(first)
                }
                
            @unknown default:
                break
            }
        }
        
        // 添加最后一个轮廓
        if !context.contourVertices.isEmpty {
            context.allContours.append(context.contourVertices)
        }
        
        // 进行三角剖分
        for contour in context.allContours {
            if contour.count > 2 {
                let vector3Vertices = contour.map { SIMD3<Float>($0.x, $0.y, 0.0) }
                tessellator.addContour(vector3Vertices)
            }
        }
        
        do {
            let result = try tessellator.tessellate(windingRule: .evenOdd, elementType: .polygons, polySize: 3)
            let tessVertices = result.vertices
            let tessIndices = result.indices
            
            // 将三角剖分结果转换为顶点数组
            for i in stride(from: 0, to: tessIndices.count, by: 3) {
                for j in 0..<3 {
                    let index = tessIndices[i + j]
                    if index < tessVertices.count {
                        let vertex = tessVertices[index]
                        vertices.append(SIMD2<Float>(vertex.x, vertex.y))
                    }
                }
            }
        } catch {
            print("三角剖分失败: \(error)")
        }
        
        return vertices
    }
}

// MARK: - 控制层：主渲染器（统一3D + 独立文字架构）

/**
 * 主渲染协调器
 * 
 * 架构职责：
 * - 系统协调：管理Metal设备、命令队列、渲染目标
 * - 渲染协调：协调3D几何渲染器和2D文字渲染器
 * - 动画管理：控制全局动画时间和帧率
 * - 变换管理：计算和更新全局变换矩阵
 * 
 * 设计理念：
 * - 单一职责：只负责协调，不处理具体渲染细节
 * - 松耦合：各个渲染器独立工作，通过接口通信
 * - 高性能：60fps动画，优化的渲染循环
 */
class SolarSystemRenderer: NSObject, MTKViewDelegate {
    // MARK: - Metal核心组件
    private let device: MTLDevice               // Metal设备
    private let mtkView: MTKView               // Metal渲染视图
    private var commandQueue: MTLCommandQueue? // 命令队列
    
    // MARK: - 渲染器组件（组合模式）
    private var geometryRenderer: GeometryRenderer?  // 3D几何渲染器
    private var textRenderer: TextRenderer?          // 2D文字渲染器
    
    // MARK: - 渲染状态和变换
    private var transformBuffer: MTLBuffer?          // 变换矩阵缓冲区
    private var depthStencilState: MTLDepthStencilState?  // 深度测试状态
    private var aspectRatio: Float = 1.0             // 屏幕宽高比
    
    // MARK: - 动画系统
    private var displayLink: CVDisplayLink?          // 显示链接（驱动动画）
    private var lastFrameTime: CFTimeInterval = 0   // 上一帧时间
    private var animationTime: Double = 0            // 累计动画时间
    
    // MARK: - 全局变换
    private var solarSystemGlobalTranslation: matrix_float4x4 = matrix_identity_float4x4
    
    /**
     * 主渲染器初始化
     * 初始化顺序：Metal设备 → 渲染器 → 状态 → 动画系统
     */
    init(device: MTLDevice, view: MTKView) {
        self.device = device
        self.mtkView = view
        self.commandQueue = device.makeCommandQueue()
        
        super.init()  // 必须在设置属性后调用
        
        setupMTKView()           // 配置Metal视图
        setupRenderers()         // 创建子渲染器
        setupDepthStencilState() // 配置深度测试
        setupTransformBuffer()   // 创建变换缓冲区
        setupDisplayLink()       // 启动动画系统
    }
    
    deinit {
        stopDisplayLink()  // 清理动画系统
    }
    
    /**
     * 配置Metal视图
     */
    private func setupMTKView() {
        mtkView.device = device
        mtkView.delegate = self
        mtkView.clearColor = MTLClearColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0)  // 白色背景
        mtkView.depthStencilPixelFormat = .depth32Float
        mtkView.sampleCount = 4           // 4倍抗锯齿
        mtkView.preferredFramesPerSecond = 60  // 60fps目标帧率
    }
    
    /**
     * 创建子渲染器
     * 设计：使用组合模式，每个渲染器专注自己的领域
     */
    private func setupRenderers() {
        // 创建几何渲染器（统一3D）
        geometryRenderer = GeometryRenderer(device: device, view: mtkView)
        
        // 创建文字渲染器（独立2D）
        textRenderer = TextRenderer(device: device, view: mtkView)
    }
    
    /**
     * 配置深度测试状态
     */
    private func setupDepthStencilState() {
        let depthStencilDescriptor = MTLDepthStencilDescriptor()
        depthStencilDescriptor.depthCompareFunction = .less    // 深度测试：较近的对象覆盖较远的
        depthStencilDescriptor.isDepthWriteEnabled = true      // 启用深度写入
        depthStencilState = device.makeDepthStencilState(descriptor: depthStencilDescriptor)
    }
    
    /**
     * 创建变换矩阵缓冲区
     */
    private func setupTransformBuffer() {
        transformBuffer = device.makeBuffer(length: MemoryLayout<Transform>.stride, options: [])
    }
    
    /**
     * 更新全局变换矩阵
     * 计算：正交投影矩阵、视图矩阵、模型矩阵
     */
    private func updateTransforms() {
        aspectRatio = Float(mtkView.drawableSize.width / mtkView.drawableSize.height)
        
        // 正交投影（适合2D显示的太阳系）
        let projectionMatrix = orthographicProjection(left: -aspectRatio, right: aspectRatio,
                                                    bottom: -1.0, top: 1.0,
                                                    near: -1.0, far: 1.0)
        
        let viewMatrix = matrix_identity_float4x4   // 单位视图矩阵
        let modelMatrix = matrix_identity_float4x4  // 单位模型矩阵
        
        let transform = Transform(modelMatrix: modelMatrix,
                                viewMatrix: viewMatrix,
                                projectionMatrix: projectionMatrix)
        
        // 更新GPU缓冲区
        let transformPointer = transformBuffer?.contents().bindMemory(to: Transform.self, capacity: 1)
        transformPointer?.pointee = transform
    }
    
    // MARK: - 动画系统
    
    /**
     * 设置显示链接驱动的动画系统
     * 技术：CVDisplayLink与屏幕刷新率同步，确保流畅动画
     */
    private func setupDisplayLink() {
        var displayLink: CVDisplayLink?
        CVDisplayLinkCreateWithActiveCGDisplays(&displayLink)
        
        guard let displayLink = displayLink else {
            fatalError("无法创建 CVDisplayLink")
        }
        
        // 动画回调函数
        let callback: CVDisplayLinkOutputCallback = { (displayLink, now, outputTime, flagsIn, flagsOut, displayLinkContext) -> CVReturn in
            let renderer = Unmanaged<SolarSystemRenderer>.fromOpaque(displayLinkContext!).takeUnretainedValue()
            renderer.updateAnimation(time: CFTimeInterval(outputTime.pointee.videoTime) / CFTimeInterval(outputTime.pointee.videoTimeScale))
            return kCVReturnSuccess
        }
        
        CVDisplayLinkSetOutputCallback(displayLink, callback, UnsafeMutableRawPointer(Unmanaged.passUnretained(self).toOpaque()))
        
        self.displayLink = displayLink
        lastFrameTime = CACurrentMediaTime()
        
        CVDisplayLinkStart(displayLink)  // 启动动画循环
    }
    
    /**
     * 停止动画系统
     */
    private func stopDisplayLink() {
        if let displayLink = displayLink {
            CVDisplayLinkStop(displayLink)
        }
    }
    
    /**
     * 更新动画状态
     * 计算：时间差值、累计动画时间、触发重绘
     */
    private func updateAnimation(time: CFTimeInterval) {
        let deltaTime = time - lastFrameTime
        lastFrameTime = time
        
        // 更新动画时间（可以调整速度倍数）
        animationTime += deltaTime * Double(SolarSystemParams.earthOrbitSpeed)
        
        // 主线程触发重绘
        DispatchQueue.main.async {
            self.mtkView.needsDisplay = true
        }
    }
    
    // MARK: - 数学辅助函数
    
    /**
     * 正交投影矩阵计算
     * 用途：将3D坐标投影到2D屏幕，保持比例不变形
     */
    private func orthographicProjection(left: Float, right: Float, bottom: Float,
                                       top: Float, near: Float, far: Float) -> matrix_float4x4 {
        let x = 2.0 / (right - left)
        let y = 2.0 / (top - bottom)
        let z = -2.0 / (far - near)
        let tx = -(right + left) / (right - left)
        let ty = -(top + bottom) / (top - bottom)
        let tz = -(far + near) / (far - near)
        
        return matrix_float4x4(columns: (
            SIMD4<Float>(x, 0, 0, 0),
            SIMD4<Float>(0, y, 0, 0),
            SIMD4<Float>(0, 0, z, 0),
            SIMD4<Float>(tx, ty, tz, 1)
        ))
    }
    
    /**
     * 平移变换矩阵
     */
    private func translationMatrix(_ translation: SIMD3<Float>) -> matrix_float4x4 {
        return matrix_float4x4(columns: (
            SIMD4<Float>(1, 0, 0, 0),
            SIMD4<Float>(0, 1, 0, 0),
            SIMD4<Float>(0, 0, 1, 0),
            SIMD4<Float>(translation.x, translation.y, translation.z, 1)
        ))
    }
}

// MARK: - MTKViewDelegate 实现

/**
 * Metal视图代理实现
 * 核心渲染循环：每帧调用draw(in:)方法
 */
extension SolarSystemRenderer {
    
    /**
     * 视图尺寸变化回调
     */
    func mtkView(_ view: MTKView, drawableSizeWillChange size: CGSize) {
        updateTransforms()  // 重新计算投影矩阵
    }
    
    /**
     * 主渲染函数
     * 渲染流程：设置渲染状态 → 渲染3D几何 → 渲染2D文字 → 提交GPU
     */
    func draw(in view: MTKView) {
        // 1. 获取渲染资源
        guard let drawable = view.currentDrawable,
              let renderPassDescriptor = view.currentRenderPassDescriptor,
              let commandBuffer = commandQueue?.makeCommandBuffer(),
              let renderEncoder = commandBuffer.makeRenderCommandEncoder(descriptor: renderPassDescriptor) else {
            return
        }
        
        // 2. 更新变换矩阵
        updateTransforms()
        
        // 3. 设置全局太阳系位置（偏左显示）
        solarSystemGlobalTranslation = translationMatrix(SIMD3<Float>(-0.7, 0.0, 0.0))
        
        // 4. 设置深度测试状态
        renderEncoder.setDepthStencilState(depthStencilState)
        
        // 5. 渲染3D几何对象（统一管线）
        geometryRenderer?.render(encoder: renderEncoder,
                               transformBuffer: transformBuffer,
                               solarSystemTransform: solarSystemGlobalTranslation,
                               animationTime: animationTime)
        
        // 6. 渲染2D文字（独立管线）
        textRenderer?.render(encoder: renderEncoder,
                           transformBuffer: transformBuffer,
                           animationTime: animationTime,
                           aspectRatio: aspectRatio)
        
        // 7. 结束渲染并提交
        renderEncoder.endEncoding()
        commandBuffer.present(drawable)
        commandBuffer.commit()
    }
}

// MARK: - 全局辅助函数

/**
 * 路径点变换函数
 * 用途：将字体路径坐标转换为屏幕坐标
 */
private func transformPathPoint(_ point: CGPoint, offset: SIMD2<Float>, scale: Float) -> SIMD2<Float> {
    return SIMD2<Float>(offset.x + Float(point.x) * scale,
                       offset.y + Float(point.y) * scale)
}

/**
 * 二次贝塞尔曲线采样
 * 算法：B(t) = (1-t)²P₀ + 2(1-t)tP₁ + t²P₂
 */
private func sampleQuadraticCurve(p0: CGPoint, p1: CGPoint, p2: CGPoint, segments: Int) -> [CGPoint] {
    var points: [CGPoint] = []
    for i in 0...segments {
        let t = CGFloat(i) / CGFloat(segments)
        let x = pow(1 - t, 2) * p0.x + 2 * (1 - t) * t * p1.x + pow(t, 2) * p2.x
        let y = pow(1 - t, 2) * p0.y + 2 * (1 - t) * t * p1.y + pow(t, 2) * p2.y
        points.append(CGPoint(x: x, y: y))
    }
    return points
}

/**
 * 三次贝塞尔曲线采样
 * 算法：B(t) = (1-t)³P₀ + 3(1-t)²tP₁ + 3(1-t)t²P₂ + t³P₃
 */
private func sampleCubicCurve(p0: CGPoint, p1: CGPoint, p2: CGPoint, p3: CGPoint, segments: Int) -> [CGPoint] {
    var points: [CGPoint] = []
    for i in 0...segments {
        let t = CGFloat(i) / CGFloat(segments)
        let x = pow(1 - t, 3) * p0.x + 3 * pow(1 - t, 2) * t * p1.x + 3 * (1 - t) * pow(t, 2) * p2.x + pow(t, 3) * p3.x
        let y = pow(1 - t, 3) * p0.y + 3 * pow(1 - t, 2) * t * p1.y + 3 * (1 - t) * pow(t, 2) * p2.y + pow(t, 3) * p3.y
        points.append(CGPoint(x: x, y: y))
    }
    return points
}

/*
 ==================================================================================
 ARCHITECTURE SUMMARY - 架构总结
 ==================================================================================
 
 本太阳系渲染器采用了现代化的分层统一架构，具有以下核心优势：
 
 🏗️ 架构优势：
 • 统一性：3D对象使用统一的顶点结构和渲染管线
 • 分离性：3D几何与2D文字完全独立，互不干扰
 • 可扩展性：新增功能只需扩展对应的渲染器
 • 高性能：预生成几何、优化的变换计算、60fps流畅动画
 
 🔧 技术栈：
 • Metal：高性能GPU渲染
 • CoreText：高质量文字处理
 • LibTessSwift：贝塞尔曲线三角剖分
 • CVDisplayLink：垂直同步动画
 
 📈 扩展方向：
 • 增加更多行星（火星、金星等）
 • 添加纹理和材质系统
 • 实现更复杂的光照模型
 • 支持3D透视投影
 • 添加交互控制（缩放、旋转）
 
 ==================================================================================
*/