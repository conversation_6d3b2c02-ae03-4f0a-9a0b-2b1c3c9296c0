//
//  SolarSystemRenderer.swift
//  Test2Dlib
//
//  Created by 王雷 on 4/6/25.
//

import Cocoa
import MetalKit
import CoreText
import LibTessSwift
import QuartzCore

// MARK: - 全局辅助函数
private func transformPathPoint(_ point: CGPoint, offset: SIMD2<Float>, scale: Float) -> SIMD2<Float> {
    return SIMD2<Float>(
        offset.x + Float(point.x) * scale,
        offset.y + Float(point.y) * scale  // 不翻转Y轴，保持字体正向显示
    )
}

// MARK: - 数据结构定义

struct Vertex {
    var position: SIMD2<Float>
}

struct Vertex3D {
    var position: SIMD3<Float>
    var normal: SIMD3<Float>
}

struct Transform {
    var modelMatrix: matrix_float4x4
    var viewMatrix: matrix_float4x4
    var projectionMatrix: matrix_float4x4
}

// MARK: - 太阳系模型参数
struct SolarSystemParams {
    static let sunRadius: Float = 0.02 // 太阳半径
    static let earthRadius: Float = 0.01 // 地球半径
    static let orbitRadius: Float = 0.3 // 增加轨道半径为原来的3倍
    static let earthOrbitSpeed: Float = 1.0 // 每秒弧度
    static let seasonChangeAngle: Float = Float.pi / 2 // 90度换一个季节
}

// MARK: - 主渲染器类
class SolarSystemRenderer: NSObject, MTKViewDelegate {
    
    // MARK: - Metal 基础组件
    private var device: MTLDevice!
    private var commandQueue: MTLCommandQueue!
    private var mtkView: MTKView!
    
    // MARK: - 渲染管线
    private var spherePipelineState: MTLRenderPipelineState?
    private var vectorPipelineState: MTLRenderPipelineState?
    private var depthStencilState: MTLDepthStencilState?
    
    // MARK: - 几何数据
    private var sunVertexBuffer: MTLBuffer?
    private var sunIndexBuffer: MTLBuffer?
    private var earthVertexBuffer: MTLBuffer?
    private var earthIndexBuffer: MTLBuffer?
    private var orbitVertexBuffer: MTLBuffer?
    private var vectorVertexBuffer: MTLBuffer?
    private var indexCount: Int = 0
    private var orbitVertexCount: Int = 0
    private var vectorVertexCount: Int = 0
    
    // MARK: - 变换矩阵
    private var transformBuffer: MTLBuffer?
    private var solarSystemGlobalTranslation: matrix_float4x4 = matrix_identity_float4x4 // 新增全局平移变量
    
    // MARK: - 动画状态
    private var animationTime: Double = 0.0
    private var displayLink: CVDisplayLink?
    private var lastFrameTime: CFTimeInterval = 0.0
    private var aspectRatio: Float = 1.0 // 新增属性
    
    // MARK: - 向量渲染组件
    private var tessellator: TessC?
    private var currentSeason: Int = 0
    private let seasons = ["春", "夏", "秋", "冬"]
    
    // MARK: - 初始化方法
    init(device: MTLDevice, view: MTKView) {
        super.init()
        
        self.device = device
        self.mtkView = view
        self.commandQueue = device.makeCommandQueue()
        
        setupMTKView()
        setupRenderPipelines()
        setupDepthStencilState()
        setupGeometry()
        setupTransformBuffer()
        setupVectorRenderer()
        setupDisplayLink()
    }
    
    deinit {
        stopDisplayLink()
    }
    
    // MARK: - MTKView 配置
    private func setupMTKView() {
        mtkView.device = device
        mtkView.delegate = self
        mtkView.clearColor = MTLClearColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0) // 白色背景
        mtkView.depthStencilPixelFormat = .depth32Float
        mtkView.sampleCount = 4 // MSAA
        mtkView.preferredFramesPerSecond = 60
    }    
    // MARK: - 渲染管线设置
    private func setupRenderPipelines() {
        guard let library = device.makeDefaultLibrary() else {
            fatalError("无法创建 Metal 库")
        }
        
        // 球体渲染管线
        setupSpherePipeline(library: library)
        
        // 向量渲染管线
        setupVectorPipeline(library: library)
    }
    
    private func setupSpherePipeline(library: MTLLibrary) {
        let vertexFunction = library.makeFunction(name: "sphere_vertex")
        let fragmentFunction = library.makeFunction(name: "sphere_fragment")
        
        // 配置顶点描述符
        let vertexDescriptor = MTLVertexDescriptor()
        
        // 位置属性 (attribute 0)
        vertexDescriptor.attributes[0].format = .float3
        vertexDescriptor.attributes[0].offset = 0
        vertexDescriptor.attributes[0].bufferIndex = 0
        
        // 法向量属性 (attribute 1)
        vertexDescriptor.attributes[1].format = .float3
        vertexDescriptor.attributes[1].offset = MemoryLayout<SIMD3<Float>>.size
        vertexDescriptor.attributes[1].bufferIndex = 0
        
        // 顶点缓冲区布局
        vertexDescriptor.layouts[0].stride = MemoryLayout<SIMD3<Float>>.size * 2  // position + normal
        vertexDescriptor.layouts[0].stepRate = 1
        vertexDescriptor.layouts[0].stepFunction = .perVertex
        
        let pipelineDescriptor = MTLRenderPipelineDescriptor()
        pipelineDescriptor.vertexFunction = vertexFunction
        pipelineDescriptor.fragmentFunction = fragmentFunction
        pipelineDescriptor.vertexDescriptor = vertexDescriptor
        pipelineDescriptor.colorAttachments[0].pixelFormat = mtkView.colorPixelFormat
        pipelineDescriptor.depthAttachmentPixelFormat = mtkView.depthStencilPixelFormat
        pipelineDescriptor.sampleCount = mtkView.sampleCount
        
        // 启用混合
        pipelineDescriptor.colorAttachments[0].isBlendingEnabled = true
        pipelineDescriptor.colorAttachments[0].rgbBlendOperation = .add
        pipelineDescriptor.colorAttachments[0].alphaBlendOperation = .add
        pipelineDescriptor.colorAttachments[0].sourceRGBBlendFactor = .sourceAlpha
        pipelineDescriptor.colorAttachments[0].sourceAlphaBlendFactor = .sourceAlpha
        pipelineDescriptor.colorAttachments[0].destinationRGBBlendFactor = .oneMinusSourceAlpha
        pipelineDescriptor.colorAttachments[0].destinationAlphaBlendFactor = .oneMinusSourceAlpha
        
        do {
            spherePipelineState = try device.makeRenderPipelineState(descriptor: pipelineDescriptor)
        } catch {
            fatalError("创建球体渲染管线失败: \(error)")
        }
    }
    
    private func setupVectorPipeline(library: MTLLibrary) {
        let vertexFunction = library.makeFunction(name: "vector_vertex")
        let fragmentFunction = library.makeFunction(name: "vector_fragment")
        
        // 配置顶点描述符
        let vertexDescriptor = MTLVertexDescriptor()
        
        // 位置属性 (attribute 0) - 2D顶点
        vertexDescriptor.attributes[0].format = .float2
        vertexDescriptor.attributes[0].offset = 0
        vertexDescriptor.attributes[0].bufferIndex = 0
        
        // 顶点缓冲区布局
        vertexDescriptor.layouts[0].stride = MemoryLayout<SIMD2<Float>>.size
        vertexDescriptor.layouts[0].stepRate = 1
        vertexDescriptor.layouts[0].stepFunction = .perVertex
        
        let pipelineDescriptor = MTLRenderPipelineDescriptor()
        pipelineDescriptor.vertexFunction = vertexFunction
        pipelineDescriptor.fragmentFunction = fragmentFunction
        pipelineDescriptor.vertexDescriptor = vertexDescriptor
        pipelineDescriptor.colorAttachments[0].pixelFormat = mtkView.colorPixelFormat
        pipelineDescriptor.depthAttachmentPixelFormat = mtkView.depthStencilPixelFormat
        pipelineDescriptor.sampleCount = mtkView.sampleCount
        
        // 启用混合
        pipelineDescriptor.colorAttachments[0].isBlendingEnabled = true
        pipelineDescriptor.colorAttachments[0].rgbBlendOperation = .add
        pipelineDescriptor.colorAttachments[0].alphaBlendOperation = .add
        pipelineDescriptor.colorAttachments[0].sourceRGBBlendFactor = .sourceAlpha
        pipelineDescriptor.colorAttachments[0].sourceAlphaBlendFactor = .sourceAlpha
        pipelineDescriptor.colorAttachments[0].destinationRGBBlendFactor = .oneMinusSourceAlpha
        pipelineDescriptor.colorAttachments[0].destinationAlphaBlendFactor = .oneMinusSourceAlpha
        
        do {
            vectorPipelineState = try device.makeRenderPipelineState(descriptor: pipelineDescriptor)
        } catch {
            fatalError("创建向量渲染管线失败: \(error)")
        }
    }    
    // MARK: - 深度模板状态
    private func setupDepthStencilState() {
        let depthStencilDescriptor = MTLDepthStencilDescriptor()
        depthStencilDescriptor.depthCompareFunction = .less
        depthStencilDescriptor.isDepthWriteEnabled = true
        depthStencilState = device.makeDepthStencilState(descriptor: depthStencilDescriptor)
    }
    
    // MARK: - 几何数据设置
    private func setupGeometry() {
        createSphereGeometry()
        createOrbitGeometry()
    }
    
    private func createSphereGeometry() {
        let (vertices, indices) = generateSphere(radius: 1.0, segments: 32)
        
        // 太阳顶点缓冲
        sunVertexBuffer = device.makeBuffer(bytes: vertices, 
                                          length: vertices.count * MemoryLayout<Vertex3D>.stride,
                                          options: [])
        
        // 地球顶点缓冲（使用相同的几何数据）
        earthVertexBuffer = device.makeBuffer(bytes: vertices,
                                            length: vertices.count * MemoryLayout<Vertex3D>.stride,
                                            options: [])
        
        // 索引缓冲
        sunIndexBuffer = device.makeBuffer(bytes: indices,
                                         length: indices.count * MemoryLayout<UInt16>.stride,
                                         options: [])
        earthIndexBuffer = device.makeBuffer(bytes: indices,
                                           length: indices.count * MemoryLayout<UInt16>.stride,
                                           options: [])
        
        indexCount = indices.count
    }
    
    private func generateSphere(radius: Float, segments: Int) -> ([Vertex3D], [UInt16]) {
        var vertices: [Vertex3D] = []
        var indices: [UInt16] = []
        
        // 生成球体顶点
        for i in 0...segments {
            let theta = Float(i) * Float.pi / Float(segments)
            for j in 0...segments {
                let phi = Float(j) * 2.0 * Float.pi / Float(segments)
                
                let x = radius * sin(theta) * cos(phi)
                let y = radius * cos(theta)
                let z = radius * sin(theta) * sin(phi)
                
                let position = SIMD3<Float>(x, y, z)
                let normal = normalize(position)
                
                vertices.append(Vertex3D(position: position, normal: normal))
            }
        }
        
        // 生成球体索引
        for i in 0..<segments {
            for j in 0..<segments {
                let first = UInt16(i * (segments + 1) + j)
                let second = UInt16(first + UInt16(segments + 1))
                
                indices.append(contentsOf: [
                    first, second, first + 1,
                    second, second + 1, first + 1
                ])
            }
        }
        
        return (vertices, indices)
    }
    
    private func createOrbitGeometry() {
        var orbitVertices: [Vertex] = []
        let segments = 720 // 增加轨道分段数以获得更平滑的圆形
        
        for i in 0...segments {
            let angle = Float(i) * 2.0 * Float.pi / Float(segments)
            let x = SolarSystemParams.orbitRadius * cos(angle)
            let y = SolarSystemParams.orbitRadius * sin(angle)
            orbitVertices.append(Vertex(position: SIMD2<Float>(x, y)))
        }
        
        orbitVertexBuffer = device.makeBuffer(bytes: orbitVertices,
                                            length: orbitVertices.count * MemoryLayout<Vertex>.stride,
                                            options: [])
        orbitVertexCount = orbitVertices.count
    }
    
    // MARK: - 变换矩阵设置
    private func setupTransformBuffer() {
        transformBuffer = device.makeBuffer(length: MemoryLayout<Transform>.stride, options: [])
    }
    
    private func updateTransforms() {
        self.aspectRatio = Float(mtkView.drawableSize.width / mtkView.drawableSize.height)
        
        // 投影矩阵（正交投影）
        // 投影矩阵（正交投影），中心在 (0,0)
        let projectionMatrix = orthographicProjection(left: -self.aspectRatio, right: self.aspectRatio,
                                                    bottom: -1.0, top: 1.0,
                                                    near: -1.0, far: 1.0)
        
        // 视图矩阵（单位矩阵）
        let viewMatrix = matrix_identity_float4x4
        
        // 全局模型矩阵（单位矩阵，具体物体位置通过各自的模型矩阵控制）
        let modelMatrix = matrix_identity_float4x4
        
        let transform = Transform(modelMatrix: modelMatrix,
                                viewMatrix: viewMatrix,
                                projectionMatrix: projectionMatrix)
        
        let transformPointer = transformBuffer?.contents().bindMemory(to: Transform.self, capacity: 1)
        transformPointer?.pointee = transform
    }
    
    // MARK: - 向量渲染设置
    private func setupVectorRenderer() {
        tessellator = TessC()
        guard tessellator != nil else {
            fatalError("无法初始化 LibTessSwift")
        }
    }    
    private func generateSeasonText() -> [Vertex] {
        var vertices: [Vertex] = []
        
        // 计算当前季节
        let seasonIndex = Int(animationTime / Double(SolarSystemParams.seasonChangeAngle)) % 4
        currentSeason = seasonIndex
        
        let seasonText = seasons[seasonIndex]
        
        // 将字体固定在屏幕右侧，轨道在左侧
        // 文字放在屏幕右侧中央位置
        // 调整文字的水平偏移，使其更靠右
        // 调整文字的水平偏移，使其更靠右，并调整垂直偏移使其居中
        let textOffset = SIMD2<Float>(self.aspectRatio - 0.3, 0.0) // 保持水平位置，垂直位置由 generateTextVertices 调整
        
        // 使用 CoreText 生成文字路径，字体大小再增加一倍 (总共 3 * 2 = 6 倍)
        if let textVertices = generateTextVertices(text: seasonText, size: 0.8 * 6.0, offset: textOffset) {
            vertices.append(contentsOf: textVertices)
        }
        
        return vertices
    }
    
    private func generateTextVertices(text: String, size: Float, offset: SIMD2<Float>) -> [Vertex]? {
        guard let tessellator = tessellator else { return nil }
        
        // 使用系统中文字体，提高字体尺寸以获得更好的精度
        let fontSize = CGFloat(144.0) // 增加字体尺寸以获得更高精度的路径
        let font = NSFont(name: "PingFang SC", size: fontSize) ?? NSFont.systemFont(ofSize: fontSize)
        
        let attributes: [NSAttributedString.Key: Any] = [.font: font]
        let attributedString = NSAttributedString(string: text, attributes: attributes)
        
        // 创建 CTLine
        let line = CTLineCreateWithAttributedString(attributedString)
        let runs = CTLineGetGlyphRuns(line)
        let runCount = CFArrayGetCount(runs)
        
        var allVertices: [Vertex] = []
        
        // 获取文本的整体尺寸
        let lineBounds = CTLineGetBoundsWithOptions(line, [])
        let textWidth = Float(lineBounds.width)
        let textHeight = Float(lineBounds.height)
        
        // 根据视图尺寸和文本尺寸计算合适的缩放因子
        // 假设我们希望文本在视图中占据一定比例，例如宽度为视图宽度的 0.5
        let viewWidth = Float(mtkView.drawableSize.width)
        let viewHeight = Float(mtkView.drawableSize.height)
        
        // 目标文本在 NDC 空间中的宽度，字体大小再增加一倍 (总共 3 * 2 = 6 倍)
        let targetNDCWidth: Float = 0.8 * 6.0 // 调整文字大小，使其更易读
        let targetNDCHeight: Float = 0.3 * 6.0 // 调整文字大小，使其更易读
        
        // 计算将文本从 CoreText 坐标系缩放到 NDC 坐标系所需的因子
        // NDC 宽度 2.0 (从 -1.0 到 1.0)，使用更精确的缩放计算
        let scaleX = targetNDCWidth / textWidth
        let scaleY = targetNDCHeight / textHeight
        let adjustedScale = min(scaleX, scaleY) * 0.8 // 添加0.8的安全系数，确保文字不会过大
        
        // 调整文本的整体偏移，使其右对齐
        // CoreText 的原点通常在文本的左下角，我们需要将其调整到 Metal 的中心
        // 对于右对齐，textOffsetX 应该将文本的右边缘对齐到 offset.x
        let textOffsetX = -textWidth * adjustedScale // 文本右对齐
        let textOffsetY = -textHeight / 2.0 * adjustedScale // 文本垂直居中
        
        // 调整文本的最终偏移，使其在屏幕上垂直居中
        // 调整文本的最终偏移，使其在屏幕上垂直居中
        // CoreText 的原点在左下角，所以我们需要将文本的中心对齐到屏幕的中心
        let finalOffset = SIMD2<Float>(offset.x + textOffsetX, offset.y - (textHeight * adjustedScale) / 2.0) // 垂直居中
        
        for runIndex in 0..<runCount {
            let run = unsafeBitCast(CFArrayGetValueAtIndex(runs, runIndex), to: CTRun.self)
            let glyphCount = CTRunGetGlyphCount(run)
            
            // 获取字形和位置
            var glyphs = Array<CGGlyph>(repeating: 0, count: glyphCount)
            var positions = Array<CGPoint>(repeating: .zero, count: glyphCount)
            
            CTRunGetGlyphs(run, CFRangeMake(0, 0), &glyphs)
            CTRunGetPositions(run, CFRangeMake(0, 0), &positions)
            
            // 获取字体
            let runAttributes = CTRunGetAttributes(run)
            let runFont = unsafeBitCast(CFDictionaryGetValue(runAttributes, 
                                                           Unmanaged.passUnretained(kCTFontAttributeName).toOpaque()),
                                      to: CTFont.self)
            
            for glyphIndex in 0..<glyphCount {
                let glyph = glyphs[glyphIndex]
                let position = positions[glyphIndex]
                
                // 创建字形路径
                if let glyphPath = CTFontCreatePathForGlyph(runFont, glyph, nil) {
                    // 将每个字形的位置 (position) 作为其自身的偏移量传递
                    let glyphLocalOffset = SIMD2<Float>(Float(position.x) * adjustedScale, Float(position.y) * adjustedScale)
                    let vertices = tessellateGlyphPath(path: glyphPath, 
                                                     offset: glyphLocalOffset, // 传递字形局部偏移
                                                     scale: adjustedScale) // 使用调整后的缩放因子
                    allVertices.append(contentsOf: vertices)
                }
            }
        }
        
        // 在所有字形顶点生成后，统一应用文本行的整体偏移
        let finalVertices = allVertices.map { vertex in
            return Vertex(position: SIMD2<Float>(finalOffset.x + vertex.position.x, finalOffset.y + vertex.position.y))
        }
        
        return finalVertices
    }    
    private func tessellateGlyphPath(path: CGPath, offset: SIMD2<Float>, scale: Float) -> [Vertex] {
        guard let tessellator = tessellator else { return [] }
        
        var vertices: [Vertex] = []
        
        // 使用类传递参数，避免闭包捕获上下文
        class PathProcessingContext {
            var contourVertices: [SIMD2<Float>] = []
            var allContours: [[SIMD2<Float>]] = []
            let offset: SIMD2<Float>
            let scale: Float
            
            init(offset: SIMD2<Float>, scale: Float) {
                self.offset = offset
                self.scale = scale
            }
        }
        
        let context = PathProcessingContext(offset: offset, scale: scale)
        
        // 使用全局函数避免闭包捕获问题
        path.apply(info: Unmanaged.passUnretained(context).toOpaque()) { contextPtr, element in
            guard let contextPtr = contextPtr else { return }
            let context = Unmanaged<PathProcessingContext>.fromOpaque(contextPtr).takeUnretainedValue()
            let points = element.pointee.points
            
            switch element.pointee.type {
            case .moveToPoint:
                // 如果已有顶点，保存当前轮廓
                if !context.contourVertices.isEmpty {
                    context.allContours.append(context.contourVertices)
                    context.contourVertices.removeAll()
                }
                let point = transformPathPoint(points[0], offset: context.offset, scale: context.scale)
                context.contourVertices.append(point)
                
            case .addLineToPoint:
                let point = transformPathPoint(points[0], offset: context.offset, scale: context.scale)
                context.contourVertices.append(point)
                
            case .addQuadCurveToPoint:
                // 获取当前路径的起点（在原始CGPath坐标系中）
                let currentPointSIMD = context.contourVertices.last
                if let currentSIMD = currentPointSIMD {
                    // 反向变换获得原始CGPath坐标，修正Y轴变换
                    let originalX = (currentSIMD.x - context.offset.x) / context.scale
                    let originalY = (currentSIMD.y - context.offset.y) / context.scale // 不翻转Y轴
                    let currentPoint = CGPoint(x: CGFloat(originalX), y: CGFloat(originalY))
                    
                    let controlPoint = points[0]
                    let endPoint = points[1]
                    let curveSegments = 20 // 增加细分段数以获得更高精度
                    let sampledPoints = sampleQuadraticCurve(p0: currentPoint, p1: controlPoint, p2: endPoint, segments: curveSegments)
                    
                    // 添加细分后的点（跳过第一个点，因为它已经是当前轮廓的最后一个点）
                    for i in 1..<sampledPoints.count {
                        let point = transformPathPoint(sampledPoints[i], offset: context.offset, scale: context.scale)
                        context.contourVertices.append(point)
                    }
                } else {
                    // 如果没有当前点，直接添加端点
                    let point = transformPathPoint(points[1], offset: context.offset, scale: context.scale)
                    context.contourVertices.append(point)
                }
                
            case .addCurveToPoint:
                // 获取当前路径的起点（在原始CGPath坐标系中）
                let currentPointSIMD = context.contourVertices.last
                if let currentSIMD = currentPointSIMD {
                    // 反向变换获得原始CGPath坐标，修正Y轴变换
                    let originalX = (currentSIMD.x - context.offset.x) / context.scale
                    let originalY = (currentSIMD.y - context.offset.y) / context.scale // 不翻转Y轴
                    let currentPoint = CGPoint(x: CGFloat(originalX), y: CGFloat(originalY))
                    
                    let controlPoint1 = points[0]
                    let controlPoint2 = points[1]
                    let endPoint = points[2]
                    let curveSegments = 20 // 增加细分段数以获得更高精度
                    let sampledPoints = sampleCubicCurve(p0: currentPoint, p1: controlPoint1, p2: controlPoint2, p3: endPoint, segments: curveSegments)
                    
                    // 添加细分后的点（跳过第一个点，因为它已经是当前轮廓的最后一个点）
                    for i in 1..<sampledPoints.count {
                        let point = transformPathPoint(sampledPoints[i], offset: context.offset, scale: context.scale)
                        context.contourVertices.append(point)
                    }
                } else {
                    // 如果没有当前点，直接添加端点
                    let point = transformPathPoint(points[2], offset: context.offset, scale: context.scale)
                    context.contourVertices.append(point)
                }
                
            case .closeSubpath:
                if let first = context.contourVertices.first, context.contourVertices.count > 2 {
                    context.contourVertices.append(first)
                }
                
            @unknown default:
                break
            }
        }
        
        // 添加最后的轮廓
        if !context.contourVertices.isEmpty { // 检查是否为空，避免添加空数组
            context.allContours.append(context.contourVertices)
        }
        
        // 将所有轮廓添加到tessellator
        for contour in context.allContours {
            if contour.count > 2 {
                let vector3Vertices = contour.map { SIMD3<Float>($0.x, $0.y, 0.0) }
                tessellator.addContour(vector3Vertices)
            }
        }
        
        // 执行三角剖分
        do {
            let result = try tessellator.tessellate(windingRule: .evenOdd, elementType: .polygons, polySize: 3)
            let tessVertices = result.vertices
            let tessIndices = result.indices
            
            print("Tessellation successful: \(tessVertices.count) vertices, \(tessIndices.count) indices")
            
            // 转换为我们的顶点格式
            for i in stride(from: 0, to: tessIndices.count, by: 3) {
                for j in 0..<3 {
                    let index = tessIndices[i + j]
                    if index < tessVertices.count {
                        let vertex = tessVertices[index]
                        vertices.append(Vertex(position: SIMD2<Float>(vertex.x, vertex.y)))
                    }
                }
            }
        } catch {
            print("Tessellation failed: \(error)")
        }
        
        return vertices
    }
    
    // MARK: - CVDisplayLink 动画控制
    private func setupDisplayLink() {
        var displayLink: CVDisplayLink?
        CVDisplayLinkCreateWithActiveCGDisplays(&displayLink)
        
        guard let displayLink = displayLink else {
            fatalError("无法创建 CVDisplayLink")
        }
        
        let callback: CVDisplayLinkOutputCallback = { (displayLink, now, outputTime, flagsIn, flagsOut, displayLinkContext) -> CVReturn in
            let renderer = Unmanaged<SolarSystemRenderer>.fromOpaque(displayLinkContext!).takeUnretainedValue()
            renderer.updateAnimation(time: CFTimeInterval(outputTime.pointee.videoTime) / CFTimeInterval(outputTime.pointee.videoTimeScale))
            return kCVReturnSuccess
        }
        
        CVDisplayLinkSetOutputCallback(displayLink, callback, UnsafeMutableRawPointer(Unmanaged.passUnretained(self).toOpaque()))
        
        self.displayLink = displayLink
        lastFrameTime = CACurrentMediaTime()
        
        CVDisplayLinkStart(displayLink)
    }    
    private func stopDisplayLink() {
        if let displayLink = displayLink {
            CVDisplayLinkStop(displayLink)
        }
    }
    
    private func updateAnimation(time: CFTimeInterval) {
        let deltaTime = time - lastFrameTime
        lastFrameTime = time
        
        animationTime += deltaTime * Double(SolarSystemParams.earthOrbitSpeed)
        
        DispatchQueue.main.async {
            self.mtkView.needsDisplay = true
        }
    }
    
    // MARK: - 数学辅助函数
    private func orthographicProjection(left: Float, right: Float, bottom: Float, 
                                       top: Float, near: Float, far: Float) -> matrix_float4x4 {
        let x = 2.0 / (right - left)
        let y = 2.0 / (top - bottom)
        let z = -2.0 / (far - near)
        let tx = -(right + left) / (right - left)
        let ty = -(top + bottom) / (top - bottom)
        let tz = -(far + near) / (far - near)
        
        return matrix_float4x4(columns: (
            SIMD4<Float>(x, 0, 0, 0),
            SIMD4<Float>(0, y, 0, 0),
            SIMD4<Float>(0, 0, z, 0),
            SIMD4<Float>(tx, ty, tz, 1)
        ))
    }
    
    private func translationMatrix(_ translation: SIMD3<Float>) -> matrix_float4x4 {
        return matrix_float4x4(columns: (
            SIMD4<Float>(1, 0, 0, 0),
            SIMD4<Float>(0, 1, 0, 0),
            SIMD4<Float>(0, 0, 1, 0),
            SIMD4<Float>(translation.x, translation.y, translation.z, 1)
        ))
    }
    
    private func scaleMatrix(_ scale: SIMD3<Float>) -> matrix_float4x4 {
        return matrix_float4x4(columns: (
            SIMD4<Float>(scale.x, 0, 0, 0),
            SIMD4<Float>(0, scale.y, 0, 0),
            SIMD4<Float>(0, 0, scale.z, 0),
            SIMD4<Float>(0, 0, 0, 1)
        ))
    }
}

// MARK: - 贝塞尔曲线细分辅助函数 (全局函数)
private func sampleQuadraticCurve(p0: CGPoint, p1: CGPoint, p2: CGPoint, segments: Int) -> [CGPoint] {
    var points: [CGPoint] = []
    for i in 0...segments {
        let t = CGFloat(i) / CGFloat(segments)
        let x = pow(1 - t, 2) * p0.x + 2 * (1 - t) * t * p1.x + pow(t, 2) * p2.x
        let y = pow(1 - t, 2) * p0.y + 2 * (1 - t) * t * p1.y + pow(t, 2) * p2.y
        points.append(CGPoint(x: x, y: y))
    }
    return points
}

private func sampleCubicCurve(p0: CGPoint, p1: CGPoint, p2: CGPoint, p3: CGPoint, segments: Int) -> [CGPoint] {
    var points: [CGPoint] = []
    for i in 0...segments {
        let t = CGFloat(i) / CGFloat(segments)
        let x = pow(1 - t, 3) * p0.x + 3 * pow(1 - t, 2) * t * p1.x + 3 * (1 - t) * pow(t, 2) * p2.x + pow(t, 3) * p3.x
        let y = pow(1 - t, 3) * p0.y + 3 * pow(1 - t, 2) * t * p1.y + 3 * (1 - t) * pow(t, 2) * p2.y + pow(t, 3) * p3.y
        points.append(CGPoint(x: x, y: y))
    }
    return points
}

// MARK: - MTKViewDelegate 实现
extension SolarSystemRenderer {
    
    func mtkView(_ view: MTKView, drawableSizeWillChange size: CGSize) {
        // 当视图尺寸改变时更新投影矩阵
        updateProjectionMatrix(size: size)
    }
    
    func updateProjectionMatrix(size: CGSize) {
        updateTransforms()
    }
    
    func draw(in view: MTKView) {
        guard let drawable = view.currentDrawable,
              let renderPassDescriptor = view.currentRenderPassDescriptor,
              let commandBuffer = commandQueue.makeCommandBuffer(),
              let renderEncoder = commandBuffer.makeRenderCommandEncoder(descriptor: renderPassDescriptor) else {
            return
        }
        
        // 更新变换矩阵
        updateTransforms()
        // 更新全局太阳系平移矩阵，使其向左侧移动
        solarSystemGlobalTranslation = translationMatrix(SIMD3<Float>(-0.7, 0.0, 0.0)) // 调整为向左移动
        
        // 设置深度模板状态
        renderEncoder.setDepthStencilState(depthStencilState)
        
        // 渲染轨道路径
        renderOrbitPath(encoder: renderEncoder)
        
        // 渲染太阳
        renderSun(encoder: renderEncoder)
        
        // 渲染地球
        renderEarth(encoder: renderEncoder)
        
        // 渲染季节文字
        renderSeasonText(encoder: renderEncoder)
        
        renderEncoder.endEncoding()
        commandBuffer.present(drawable)
        commandBuffer.commit()
    }    
    private func renderOrbitPath(encoder: MTLRenderCommandEncoder) {
        guard let pipelineState = vectorPipelineState,
              let orbitBuffer = orbitVertexBuffer else { return }
        
        encoder.setRenderPipelineState(pipelineState)
        encoder.setVertexBuffer(orbitBuffer, offset: 0, index: 0)
        encoder.setVertexBuffer(transformBuffer, offset: 0, index: 1)
        
        // 轨道变换矩阵：只应用全局太阳系平移，不额外缩放
        var orbitTransform = solarSystemGlobalTranslation
        encoder.setVertexBytes(&orbitTransform, length: MemoryLayout<matrix_float4x4>.stride, index: 2)
        
        // 设置黑色颜色
        var color = SIMD4<Float>(0.0, 0.0, 0.0, 1.0)
        encoder.setFragmentBytes(&color, length: MemoryLayout<SIMD4<Float>>.stride, index: 0)
        
        encoder.drawPrimitives(type: .lineStrip, vertexStart: 0, vertexCount: orbitVertexCount)
    }
    
    private func renderSun(encoder: MTLRenderCommandEncoder) {
        guard let pipelineState = spherePipelineState,
              let vertexBuffer = sunVertexBuffer,
              let indexBuffer = sunIndexBuffer else { return }
        
        encoder.setRenderPipelineState(pipelineState)
        encoder.setVertexBuffer(vertexBuffer, offset: 0, index: 0)
        encoder.setVertexBuffer(transformBuffer, offset: 0, index: 1)
        
        // 太阳的变换矩阵：先缩放，再应用全局平移（太阳位于轨道中心）
        let sunScale = scaleMatrix(SIMD3<Float>(SolarSystemParams.sunRadius, 
                                              SolarSystemParams.sunRadius, 
                                              SolarSystemParams.sunRadius))
        var sunTransform = solarSystemGlobalTranslation * sunScale
        encoder.setVertexBytes(&sunTransform, length: MemoryLayout<matrix_float4x4>.stride, index: 2)
        
        // 设置黑色颜色
        var color = SIMD4<Float>(0.0, 0.0, 0.0, 1.0)
        encoder.setFragmentBytes(&color, length: MemoryLayout<SIMD4<Float>>.stride, index: 0)
        
        encoder.drawIndexedPrimitives(type: .triangle, 
                                    indexCount: indexCount, 
                                    indexType: .uint16, 
                                    indexBuffer: indexBuffer, 
                                    indexBufferOffset: 0)
    }    
    private func renderEarth(encoder: MTLRenderCommandEncoder) {
        guard let pipelineState = spherePipelineState,
              let vertexBuffer = earthVertexBuffer,
              let indexBuffer = earthIndexBuffer else { return }
        
        encoder.setRenderPipelineState(pipelineState)
        encoder.setVertexBuffer(vertexBuffer, offset: 0, index: 0)
        encoder.setVertexBuffer(transformBuffer, offset: 0, index: 1)
        
        // 计算地球在轨道上的位置
        let angle = Float(animationTime)
        let earthX = SolarSystemParams.orbitRadius * cos(angle)
        let earthY = SolarSystemParams.orbitRadius * sin(angle)
        
        // 地球的变换矩阵：先缩放，再移动到轨道位置，最后应用全局平移
        let earthScale = scaleMatrix(SIMD3<Float>(SolarSystemParams.earthRadius, 
                                                SolarSystemParams.earthRadius, 
                                                SolarSystemParams.earthRadius))
        let earthOrbitTranslation = translationMatrix(SIMD3<Float>(earthX, earthY, 0.0))
        var earthTransform = solarSystemGlobalTranslation * earthOrbitTranslation * earthScale
        
        encoder.setVertexBytes(&earthTransform, length: MemoryLayout<matrix_float4x4>.stride, index: 2)
        
        // 设置黑色颜色
        var color = SIMD4<Float>(0.0, 0.0, 0.0, 1.0)
        encoder.setFragmentBytes(&color, length: MemoryLayout<SIMD4<Float>>.stride, index: 0)
        
        encoder.drawIndexedPrimitives(type: .triangle, 
                                    indexCount: indexCount, 
                                    indexType: .uint16, 
                                    indexBuffer: indexBuffer, 
                                    indexBufferOffset: 0)
    }
    
    private func renderSeasonText(encoder: MTLRenderCommandEncoder) {
        guard let pipelineState = vectorPipelineState else { return }
        
        // 生成季节文字的三角形
        let textVertices = generateSeasonText()
        guard !textVertices.isEmpty else { return }
        
        // 创建临时顶点缓冲
        guard let textBuffer = device.makeBuffer(bytes: textVertices,
                                               length: textVertices.count * MemoryLayout<Vertex>.stride,
                                               options: []) else { return }
        
        encoder.setRenderPipelineState(pipelineState)
        encoder.setVertexBuffer(textBuffer, offset: 0, index: 0)
        encoder.setVertexBuffer(transformBuffer, offset: 0, index: 1)
        
        // 设置黑色颜色
        var color = SIMD4<Float>(0.0, 0.0, 0.0, 1.0)
        encoder.setFragmentBytes(&color, length: MemoryLayout<SIMD4<Float>>.stride, index: 0)
        
        encoder.drawPrimitives(type: .triangle, vertexStart: 0, vertexCount: textVertices.count)
    }
}
