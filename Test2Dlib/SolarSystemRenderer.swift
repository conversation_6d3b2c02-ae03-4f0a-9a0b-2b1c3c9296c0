//
//  SolarSystemRenderer.swift
//  Test2Dlib
//
//  Created by 王雷 on 4/6/25.
//

import Foundation
import MetalKit
import CoreText
import LibTessSwift

// MARK: - 数据结构定义

// 统一使用3D顶点结构
struct UnifiedVertex {
    var position: SIMD3<Float>
    var normal: SIMD3<Float>
}

struct Transform {
    var modelMatrix: matrix_float4x4
    var viewMatrix: matrix_float4x4
    var projectionMatrix: matrix_float4x4
}

// MARK: - 太阳系模型参数
struct SolarSystemParams {
    static let sunRadius: Float = 0.02 // 太阳半径
    static let earthRadius: Float = 0.01 // 地球半径
    static let orbitRadius: Float = 0.3 // 轨道半径
    static let earthOrbitSpeed: Float = 1.0 // 每秒弧度
    static let seasonChangeAngle: Float = Float.pi / 2 // 90度换一个季节
}

// MARK: - 几何渲染器（统一3D管线）
class GeometryRenderer {
    private let device: MTLDevice
    private var pipelineState: MTLRenderPipelineState?
    
    // 几何数据
    private var sphereVertexBuffer: MTLBuffer?
    private var sphereIndexBuffer: MTLBuffer?
    private var orbitVertexBuffer: MTLBuffer?
    private var sphereIndexCount: Int = 0
    private var orbitVertexCount: Int = 0
    
    init(device: MTLDevice, view: MTKView) {
        self.device = device
        setupPipeline(view: view)
        setupGeometry()
    }
    
    private func setupPipeline(view: MTKView) {
        guard let library = device.makeDefaultLibrary() else {
            fatalError("无法创建 Metal 库")
        }
        
        guard let vertexFunction = library.makeFunction(name: "unified_vertex"),
              let fragmentFunction = library.makeFunction(name: "unified_fragment") else {
            fatalError("无法创建着色器函数")
        }
        
        // 统一的3D顶点描述符
        let vertexDescriptor = MTLVertexDescriptor()
        
        // 位置属性 (attribute 0) - 3D位置
        vertexDescriptor.attributes[0].format = .float3
        vertexDescriptor.attributes[0].offset = 0
        vertexDescriptor.attributes[0].bufferIndex = 0
        
        // 法向量属性 (attribute 1) - 3D法向量
        vertexDescriptor.attributes[1].format = .float3
        vertexDescriptor.attributes[1].offset = MemoryLayout<SIMD3<Float>>.size
        vertexDescriptor.attributes[1].bufferIndex = 0
        
        // 顶点缓冲区布局
        vertexDescriptor.layouts[0].stride = MemoryLayout<UnifiedVertex>.stride
        vertexDescriptor.layouts[0].stepRate = 1
        vertexDescriptor.layouts[0].stepFunction = .perVertex
        
        let pipelineDescriptor = MTLRenderPipelineDescriptor()
        pipelineDescriptor.vertexFunction = vertexFunction
        pipelineDescriptor.fragmentFunction = fragmentFunction
        pipelineDescriptor.vertexDescriptor = vertexDescriptor
        pipelineDescriptor.colorAttachments[0].pixelFormat = view.colorPixelFormat
        pipelineDescriptor.depthAttachmentPixelFormat = view.depthStencilPixelFormat
        pipelineDescriptor.sampleCount = view.sampleCount
        
        do {
            pipelineState = try device.makeRenderPipelineState(descriptor: pipelineDescriptor)
        } catch {
            fatalError("创建统一渲染管线失败: \(error)")
        }
    }
    
    private func setupGeometry() {
        // 创建3D球体几何
        let (sphereVertices, sphereIndices) = generateSphere3D(radius: 1.0, segments: 32)
        sphereVertexBuffer = device.makeBuffer(bytes: sphereVertices,
                                             length: sphereVertices.count * MemoryLayout<UnifiedVertex>.stride,
                                             options: [])
        sphereIndexBuffer = device.makeBuffer(bytes: sphereIndices,
                                            length: sphereIndices.count * MemoryLayout<UInt16>.stride,
                                            options: [])
        sphereIndexCount = sphereIndices.count
        
        // 创建3D轨道几何（z=0平面上的圆环）
        let orbitVertices = generateOrbit3D(segments: 720)
        orbitVertexBuffer = device.makeBuffer(bytes: orbitVertices,
                                            length: orbitVertices.count * MemoryLayout<UnifiedVertex>.stride,
                                            options: [])
        orbitVertexCount = orbitVertices.count
    }
    
    private func generateSphere3D(radius: Float, segments: Int) -> ([UnifiedVertex], [UInt16]) {
        var vertices: [UnifiedVertex] = []
        var indices: [UInt16] = []
        
        // 生成球体顶点
        for i in 0...segments {
            let theta = Float(i) * Float.pi / Float(segments)
            for j in 0...segments {
                let phi = Float(j) * 2.0 * Float.pi / Float(segments)
                
                let x = radius * sin(theta) * cos(phi)
                let y = radius * cos(theta)
                let z = radius * sin(theta) * sin(phi)
                
                let position = SIMD3<Float>(x, y, z)
                let normal = normalize(position) // 球体的法向量就是归一化的位置向量
                
                vertices.append(UnifiedVertex(position: position, normal: normal))
            }
        }
        
        // 生成球体索引
        for i in 0..<segments {
            for j in 0..<segments {
                let current = UInt16(i * (segments + 1) + j)
                let next = UInt16(i * (segments + 1) + (j + 1))
                let below = UInt16((i + 1) * (segments + 1) + j)
                let belowNext = UInt16((i + 1) * (segments + 1) + (j + 1))
                
                // 第一个三角形
                indices.append(current)
                indices.append(below)
                indices.append(next)
                
                // 第二个三角形
                indices.append(next)
                indices.append(below)
                indices.append(belowNext)
            }
        }
        
        return (vertices, indices)
    }
    
    private func generateOrbit3D(segments: Int) -> [UnifiedVertex] {
        var vertices: [UnifiedVertex] = []
        let normal = SIMD3<Float>(0.0, 0.0, 1.0) // 轨道在xy平面，法向量指向z轴
        
        for i in 0...segments {
            let angle = Float(i) * 2.0 * Float.pi / Float(segments)
            let x = cos(angle)
            let y = sin(angle)
            let z: Float = 0.0 // 轨道在z=0平面
            
            let position = SIMD3<Float>(x, y, z)
            vertices.append(UnifiedVertex(position: position, normal: normal))
        }
        
        return vertices
    }
    
    func render(encoder: MTLRenderCommandEncoder, 
                transformBuffer: MTLBuffer?,
                solarSystemTransform: matrix_float4x4,
                animationTime: Double) {
        guard let pipelineState = pipelineState else { return }
        
        encoder.setRenderPipelineState(pipelineState)
        encoder.setVertexBuffer(transformBuffer, offset: 0, index: 1)
        
        // 渲染轨道
        renderOrbit(encoder: encoder, globalTransform: solarSystemTransform)
        
        // 渲染太阳
        renderSun(encoder: encoder, globalTransform: solarSystemTransform)
        
        // 渲染地球
        renderEarth(encoder: encoder, globalTransform: solarSystemTransform, animationTime: animationTime)
    }
    
    private func renderOrbit(encoder: MTLRenderCommandEncoder, globalTransform: matrix_float4x4) {
        guard let orbitBuffer = orbitVertexBuffer else { return }
        
        encoder.setVertexBuffer(orbitBuffer, offset: 0, index: 0)
        
        // 轨道变换：单位圆缩放到轨道大小
        let orbitScale = scaleMatrix(SIMD3<Float>(SolarSystemParams.orbitRadius,
                                                 SolarSystemParams.orbitRadius,
                                                 1.0))
        var orbitTransform = globalTransform * orbitScale
        encoder.setVertexBytes(&orbitTransform, length: MemoryLayout<matrix_float4x4>.stride, index: 2)
        
        // 设置黑色
        var color = SIMD4<Float>(0.0, 0.0, 0.0, 1.0)
        encoder.setFragmentBytes(&color, length: MemoryLayout<SIMD4<Float>>.stride, index: 0)
        
        encoder.drawPrimitives(type: .lineStrip, vertexStart: 0, vertexCount: orbitVertexCount)
    }
    
    private func renderSun(encoder: MTLRenderCommandEncoder, globalTransform: matrix_float4x4) {
        guard let vertexBuffer = sphereVertexBuffer,
              let indexBuffer = sphereIndexBuffer else { return }
        
        encoder.setVertexBuffer(vertexBuffer, offset: 0, index: 0)
        
        // 太阳变换：缩放到太阳大小
        let sunScale = scaleMatrix(SIMD3<Float>(SolarSystemParams.sunRadius,
                                              SolarSystemParams.sunRadius,
                                              SolarSystemParams.sunRadius))
        var sunTransform = globalTransform * sunScale
        encoder.setVertexBytes(&sunTransform, length: MemoryLayout<matrix_float4x4>.stride, index: 2)
        
        // 设置黑色
        var color = SIMD4<Float>(0.0, 0.0, 0.0, 1.0)
        encoder.setFragmentBytes(&color, length: MemoryLayout<SIMD4<Float>>.stride, index: 0)
        
        encoder.drawIndexedPrimitives(type: .triangle,
                                    indexCount: sphereIndexCount,
                                    indexType: .uint16,
                                    indexBuffer: indexBuffer,
                                    indexBufferOffset: 0)
    }
    
    private func renderEarth(encoder: MTLRenderCommandEncoder, globalTransform: matrix_float4x4, animationTime: Double) {
        guard let vertexBuffer = sphereVertexBuffer,
              let indexBuffer = sphereIndexBuffer else { return }
        
        encoder.setVertexBuffer(vertexBuffer, offset: 0, index: 0)
        
        // 地球变换：缩放 -> 轨道位置 -> 全局变换
        let earthScale = scaleMatrix(SIMD3<Float>(SolarSystemParams.earthRadius,
                                                SolarSystemParams.earthRadius,
                                                SolarSystemParams.earthRadius))
        
        // 地球轨道位置（使用动画时间）
        let angle = Float(animationTime)
        let earthX = SolarSystemParams.orbitRadius * cos(angle)
        let earthY = SolarSystemParams.orbitRadius * sin(angle)
        let earthOrbitTranslation = translationMatrix(SIMD3<Float>(earthX, earthY, 0.0))
        
        var earthTransform = globalTransform * earthOrbitTranslation * earthScale
        encoder.setVertexBytes(&earthTransform, length: MemoryLayout<matrix_float4x4>.stride, index: 2)
        
        // 设置黑色
        var color = SIMD4<Float>(0.0, 0.0, 0.0, 1.0)
        encoder.setFragmentBytes(&color, length: MemoryLayout<SIMD4<Float>>.stride, index: 0)
        
        encoder.drawIndexedPrimitives(type: .triangle,
                                    indexCount: sphereIndexCount,
                                    indexType: .uint16,
                                    indexBuffer: indexBuffer,
                                    indexBufferOffset: 0)
    }
    
    // 数学辅助函数
    private func translationMatrix(_ translation: SIMD3<Float>) -> matrix_float4x4 {
        return matrix_float4x4(columns: (
            SIMD4<Float>(1, 0, 0, 0),
            SIMD4<Float>(0, 1, 0, 0),
            SIMD4<Float>(0, 0, 1, 0),
            SIMD4<Float>(translation.x, translation.y, translation.z, 1)
        ))
    }
    
    private func scaleMatrix(_ scale: SIMD3<Float>) -> matrix_float4x4 {
        return matrix_float4x4(columns: (
            SIMD4<Float>(scale.x, 0, 0, 0),
            SIMD4<Float>(0, scale.y, 0, 0),
            SIMD4<Float>(0, 0, scale.z, 0),
            SIMD4<Float>(0, 0, 0, 1)
        ))
    }
}
// MARK: - 文字渲染器（独立2D文字层）
class TextRenderer {
    private let device: MTLDevice
    private var pipelineState: MTLRenderPipelineState?
    private var tessellator: TessC?
    private let seasons = ["春", "夏", "秋", "冬"]
    private var currentSeason: Int = 0
    
    init(device: MTLDevice, view: MTKView) {
        self.device = device
        setupPipeline(view: view)
        setupTessellator()
    }
    
    private func setupPipeline(view: MTKView) {
        guard let library = device.makeDefaultLibrary() else {
            fatalError("无法创建 Metal 库")
        }
        
        guard let vertexFunction = library.makeFunction(name: "text_vertex"),
              let fragmentFunction = library.makeFunction(name: "text_fragment") else {
            fatalError("无法创建文字着色器函数")
        }
        
        // 2D文字顶点描述符
        let vertexDescriptor = MTLVertexDescriptor()
        
        // 位置属性 - 2D位置
        vertexDescriptor.attributes[0].format = .float2
        vertexDescriptor.attributes[0].offset = 0
        vertexDescriptor.attributes[0].bufferIndex = 0
        
        vertexDescriptor.layouts[0].stride = MemoryLayout<SIMD2<Float>>.size
        vertexDescriptor.layouts[0].stepRate = 1
        vertexDescriptor.layouts[0].stepFunction = .perVertex
        
        let pipelineDescriptor = MTLRenderPipelineDescriptor()
        pipelineDescriptor.vertexFunction = vertexFunction
        pipelineDescriptor.fragmentFunction = fragmentFunction
        pipelineDescriptor.vertexDescriptor = vertexDescriptor
        pipelineDescriptor.colorAttachments[0].pixelFormat = view.colorPixelFormat
        pipelineDescriptor.depthAttachmentPixelFormat = view.depthStencilPixelFormat
        pipelineDescriptor.sampleCount = view.sampleCount
        
        // 启用混合
        pipelineDescriptor.colorAttachments[0].isBlendingEnabled = true
        pipelineDescriptor.colorAttachments[0].rgbBlendOperation = .add
        pipelineDescriptor.colorAttachments[0].alphaBlendOperation = .add
        pipelineDescriptor.colorAttachments[0].sourceRGBBlendFactor = .sourceAlpha
        pipelineDescriptor.colorAttachments[0].sourceAlphaBlendFactor = .sourceAlpha
        pipelineDescriptor.colorAttachments[0].destinationRGBBlendFactor = .oneMinusSourceAlpha
        pipelineDescriptor.colorAttachments[0].destinationAlphaBlendFactor = .oneMinusSourceAlpha
        
        do {
            pipelineState = try device.makeRenderPipelineState(descriptor: pipelineDescriptor)
        } catch {
            fatalError("创建文字渲染管线失败: \(error)")
        }
    }
    
    private func setupTessellator() {
        tessellator = TessC()
        guard tessellator != nil else {
            fatalError("无法初始化 LibTessSwift")
        }
    }
    
    func render(encoder: MTLRenderCommandEncoder,
                transformBuffer: MTLBuffer?,
                animationTime: Double,
                aspectRatio: Float) {
        guard let pipelineState = pipelineState else { return }
        
        // 计算当前季节
        let seasonIndex = Int(animationTime / Double(SolarSystemParams.seasonChangeAngle)) % 4
        currentSeason = seasonIndex
        let seasonText = seasons[seasonIndex]
        
        // 生成文字几何
        let textOffset = SIMD2<Float>(aspectRatio - 0.3, 0.0)
        guard let textVertices = generateTextVertices(text: seasonText, 
                                                    size: 0.8 * 6.0, 
                                                    offset: textOffset,
                                                    aspectRatio: aspectRatio) else { return }
        
        guard !textVertices.isEmpty else { return }
        
        // 创建临时顶点缓冲
        guard let textBuffer = device.makeBuffer(bytes: textVertices,
                                               length: textVertices.count * MemoryLayout<SIMD2<Float>>.stride,
                                               options: []) else { return }
        
        encoder.setRenderPipelineState(pipelineState)
        encoder.setVertexBuffer(textBuffer, offset: 0, index: 0)
        encoder.setVertexBuffer(transformBuffer, offset: 0, index: 1)
        
        // 文字使用单位矩阵（位置已在顶点中计算）
        var textTransform = matrix_identity_float4x4
        encoder.setVertexBytes(&textTransform, length: MemoryLayout<matrix_float4x4>.stride, index: 2)
        
        // 设置黑色
        var color = SIMD4<Float>(0.0, 0.0, 0.0, 1.0)
        encoder.setFragmentBytes(&color, length: MemoryLayout<SIMD4<Float>>.stride, index: 0)
        
        encoder.drawPrimitives(type: .triangle, vertexStart: 0, vertexCount: textVertices.count)
    }
    
    private func generateTextVertices(text: String, 
                                    size: Float, 
                                    offset: SIMD2<Float>,
                                    aspectRatio: Float) -> [SIMD2<Float>]? {
        guard let tessellator = tessellator else { return nil }
        
        let fontSize = CGFloat(144.0)
        let font = NSFont(name: "PingFang SC", size: fontSize) ?? NSFont.systemFont(ofSize: fontSize)
        
        let attributes: [NSAttributedString.Key: Any] = [.font: font]
        let attributedString = NSAttributedString(string: text, attributes: attributes)
        
        let line = CTLineCreateWithAttributedString(attributedString)
        let runs = CTLineGetGlyphRuns(line)
        let runCount = CFArrayGetCount(runs)
        
        var allVertices: [SIMD2<Float>] = []
        
        let lineBounds = CTLineGetBoundsWithOptions(line, [])
        let textWidth = Float(lineBounds.width)
        let textHeight = Float(lineBounds.height)
        
        let targetNDCWidth: Float = 0.8 * 6.0
        let targetNDCHeight: Float = 0.3 * 6.0
        
        let scaleX = targetNDCWidth / textWidth
        let scaleY = targetNDCHeight / textHeight
        let adjustedScale = min(scaleX, scaleY) * 0.8
        
        let textOffsetX = -textWidth * adjustedScale
        let textOffsetY = -textHeight / 2.0 * adjustedScale
        
        let finalOffset = SIMD2<Float>(offset.x + textOffsetX, 
                                     offset.y - (textHeight * adjustedScale) / 2.0)
        
        for runIndex in 0..<runCount {
            let run = unsafeBitCast(CFArrayGetValueAtIndex(runs, runIndex), to: CTRun.self)
            let glyphCount = CTRunGetGlyphCount(run)
            
            var glyphs = Array<CGGlyph>(repeating: 0, count: glyphCount)
            var positions = Array<CGPoint>(repeating: .zero, count: glyphCount)
            
            CTRunGetGlyphs(run, CFRangeMake(0, 0), &glyphs)
            CTRunGetPositions(run, CFRangeMake(0, 0), &positions)
            
            let runAttributes = CTRunGetAttributes(run)
            let runFont = unsafeBitCast(CFDictionaryGetValue(runAttributes,
                                                           Unmanaged.passUnretained(kCTFontAttributeName).toOpaque()),
                                      to: CTFont.self)
            
            for glyphIndex in 0..<glyphCount {
                let glyph = glyphs[glyphIndex]
                let position = positions[glyphIndex]
                
                if let glyphPath = CTFontCreatePathForGlyph(runFont, glyph, nil) {
                    let glyphOffset = SIMD2<Float>(Float(position.x) * adjustedScale,
                                                 Float(position.y) * adjustedScale)
                    let glyphVertices = tessellateGlyphPath(path: glyphPath,
                                                          offset: glyphOffset,
                                                          scale: adjustedScale)
                    allVertices.append(contentsOf: glyphVertices)
                }
            }
        }
        
        // 应用最终偏移
        let finalVertices = allVertices.map { vertex in
            return SIMD2<Float>(finalOffset.x + vertex.x, finalOffset.y + vertex.y)
        }
        
        return finalVertices
    }
    
    private func tessellateGlyphPath(path: CGPath, offset: SIMD2<Float>, scale: Float) -> [SIMD2<Float>] {
        guard let tessellator = tessellator else { return [] }
        
        var vertices: [SIMD2<Float>] = []
        
        class PathProcessingContext {
            var contourVertices: [SIMD2<Float>] = []
            var allContours: [[SIMD2<Float>]] = []
            let offset: SIMD2<Float>
            let scale: Float
            
            init(offset: SIMD2<Float>, scale: Float) {
                self.offset = offset
                self.scale = scale
            }
        }
        
        let context = PathProcessingContext(offset: offset, scale: scale)
        
        path.apply(info: Unmanaged.passUnretained(context).toOpaque()) { contextPtr, element in
            guard let contextPtr = contextPtr else { return }
            let context = Unmanaged<PathProcessingContext>.fromOpaque(contextPtr).takeUnretainedValue()
            let points = element.pointee.points
            
            switch element.pointee.type {
            case .moveToPoint:
                if !context.contourVertices.isEmpty {
                    context.allContours.append(context.contourVertices)
                    context.contourVertices.removeAll()
                }
                let point = transformPathPoint(points[0], offset: context.offset, scale: context.scale)
                context.contourVertices.append(point)
                
            case .addLineToPoint:
                let point = transformPathPoint(points[0], offset: context.offset, scale: context.scale)
                context.contourVertices.append(point)
                
            case .addQuadCurveToPoint:
                let currentPointSIMD = context.contourVertices.last
                if let currentSIMD = currentPointSIMD {
                    let originalX = (currentSIMD.x - context.offset.x) / context.scale
                    let originalY = (currentSIMD.y - context.offset.y) / context.scale
                    let currentPoint = CGPoint(x: CGFloat(originalX), y: CGFloat(originalY))
                    
                    let controlPoint = points[0]
                    let endPoint = points[1]
                    let curveSegments = 20
                    let sampledPoints = sampleQuadraticCurve(p0: currentPoint, p1: controlPoint, p2: endPoint, segments: curveSegments)
                    
                    for i in 1..<sampledPoints.count {
                        let point = transformPathPoint(sampledPoints[i], offset: context.offset, scale: context.scale)
                        context.contourVertices.append(point)
                    }
                } else {
                    let point = transformPathPoint(points[1], offset: context.offset, scale: context.scale)
                    context.contourVertices.append(point)
                }
                
            case .addCurveToPoint:
                let currentPointSIMD = context.contourVertices.last
                if let currentSIMD = currentPointSIMD {
                    let originalX = (currentSIMD.x - context.offset.x) / context.scale
                    let originalY = (currentSIMD.y - context.offset.y) / context.scale
                    let currentPoint = CGPoint(x: CGFloat(originalX), y: CGFloat(originalY))
                    
                    let controlPoint1 = points[0]
                    let controlPoint2 = points[1]
                    let endPoint = points[2]
                    let curveSegments = 20
                    let sampledPoints = sampleCubicCurve(p0: currentPoint, p1: controlPoint1, p2: controlPoint2, p3: endPoint, segments: curveSegments)
                    
                    for i in 1..<sampledPoints.count {
                        let point = transformPathPoint(sampledPoints[i], offset: context.offset, scale: context.scale)
                        context.contourVertices.append(point)
                    }
                } else {
                    let point = transformPathPoint(points[2], offset: context.offset, scale: context.scale)
                    context.contourVertices.append(point)
                }
                
            case .closeSubpath:
                if let first = context.contourVertices.first, context.contourVertices.count > 2 {
                    context.contourVertices.append(first)
                }
                
            @unknown default:
                break
            }
        }
        
        if !context.contourVertices.isEmpty {
            context.allContours.append(context.contourVertices)
        }
        
        // 三角剖分
        for contour in context.allContours {
            if contour.count > 2 {
                let vector3Vertices = contour.map { SIMD3<Float>($0.x, $0.y, 0.0) }
                tessellator.addContour(vector3Vertices)
            }
        }
        
        do {
            let result = try tessellator.tessellate(windingRule: .evenOdd, elementType: .polygons, polySize: 3)
            let tessVertices = result.vertices
            let tessIndices = result.indices
            
            for i in stride(from: 0, to: tessIndices.count, by: 3) {
                for j in 0..<3 {
                    let index = tessIndices[i + j]
                    if index < tessVertices.count {
                        let vertex = tessVertices[index]
                        vertices.append(SIMD2<Float>(vertex.x, vertex.y))
                    }
                }
            }
        } catch {
            print("Tessellation failed: \(error)")
        }
        
        return vertices
    }
}// MARK: - 主渲染器（统一3D + 独立文字架构）
class SolarSystemRenderer: NSObject, MTKViewDelegate {
    // Metal 核心组件
    private let device: MTLDevice
    private let mtkView: MTKView
    private var commandQueue: MTLCommandQueue?
    
    // 渲染器组件
    private var geometryRenderer: GeometryRenderer?
    private var textRenderer: TextRenderer?
    
    // 变换和状态
    private var transformBuffer: MTLBuffer?
    private var depthStencilState: MTLDepthStencilState?
    private var aspectRatio: Float = 1.0
    
    // 动画系统
    private var displayLink: CVDisplayLink?
    private var lastFrameTime: CFTimeInterval = 0
    private var animationTime: Double = 0
    
    // 全局变换
    private var solarSystemGlobalTranslation: matrix_float4x4 = matrix_identity_float4x4
    
    init(device: MTLDevice, view: MTKView) {
        super.init()
        
        self.device = device
        self.mtkView = view
        self.commandQueue = device.makeCommandQueue()
        
        setupMTKView()
        setupRenderers()
        setupDepthStencilState()
        setupTransformBuffer()
        setupDisplayLink()
    }
    
    deinit {
        stopDisplayLink()
    }
    
    private func setupMTKView() {
        mtkView.device = device
        mtkView.delegate = self
        mtkView.clearColor = MTLClearColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0)
        mtkView.depthStencilPixelFormat = .depth32Float
        mtkView.sampleCount = 4
        mtkView.preferredFramesPerSecond = 60
    }
    
    private func setupRenderers() {
        // 创建几何渲染器（统一3D）
        geometryRenderer = GeometryRenderer(device: device, view: mtkView)
        
        // 创建文字渲染器（独立2D）
        textRenderer = TextRenderer(device: device, view: mtkView)
    }
    
    private func setupDepthStencilState() {
        let depthStencilDescriptor = MTLDepthStencilDescriptor()
        depthStencilDescriptor.depthCompareFunction = .less
        depthStencilDescriptor.isDepthWriteEnabled = true
        depthStencilState = device.makeDepthStencilState(descriptor: depthStencilDescriptor)
    }
    
    private func setupTransformBuffer() {
        transformBuffer = device.makeBuffer(length: MemoryLayout<Transform>.stride, options: [])
    }
    
    private func updateTransforms() {
        aspectRatio = Float(mtkView.drawableSize.width / mtkView.drawableSize.height)
        
        // 正交投影
        let projectionMatrix = orthographicProjection(left: -aspectRatio, right: aspectRatio,
                                                    bottom: -1.0, top: 1.0,
                                                    near: -1.0, far: 1.0)
        
        let viewMatrix = matrix_identity_float4x4
        let modelMatrix = matrix_identity_float4x4
        
        let transform = Transform(modelMatrix: modelMatrix,
                                viewMatrix: viewMatrix,
                                projectionMatrix: projectionMatrix)
        
        let transformPointer = transformBuffer?.contents().bindMemory(to: Transform.self, capacity: 1)
        transformPointer?.pointee = transform
    }
    
    // MARK: - 动画系统
    private func setupDisplayLink() {
        var displayLink: CVDisplayLink?
        CVDisplayLinkCreateWithActiveCGDisplays(&displayLink)
        
        guard let displayLink = displayLink else {
            fatalError("无法创建 CVDisplayLink")
        }
        
        let callback: CVDisplayLinkOutputCallback = { (displayLink, now, outputTime, flagsIn, flagsOut, displayLinkContext) -> CVReturn in
            let renderer = Unmanaged<SolarSystemRenderer>.fromOpaque(displayLinkContext!).takeUnretainedValue()
            renderer.updateAnimation(time: CFTimeInterval(outputTime.pointee.videoTime) / CFTimeInterval(outputTime.pointee.videoTimeScale))
            return kCVReturnSuccess
        }
        
        CVDisplayLinkSetOutputCallback(displayLink, callback, UnsafeMutableRawPointer(Unmanaged.passUnretained(self).toOpaque()))
        
        self.displayLink = displayLink
        lastFrameTime = CACurrentMediaTime()
        
        CVDisplayLinkStart(displayLink)
    }
    
    private func stopDisplayLink() {
        if let displayLink = displayLink {
            CVDisplayLinkStop(displayLink)
        }
    }
    
    private func updateAnimation(time: CFTimeInterval) {
        let deltaTime = time - lastFrameTime
        lastFrameTime = time
        
        animationTime += deltaTime * Double(SolarSystemParams.earthOrbitSpeed)
        
        DispatchQueue.main.async {
            self.mtkView.needsDisplay = true
        }
    }
    
    // MARK: - 数学辅助函数
    private func orthographicProjection(left: Float, right: Float, bottom: Float,
                                       top: Float, near: Float, far: Float) -> matrix_float4x4 {
        let x = 2.0 / (right - left)
        let y = 2.0 / (top - bottom)
        let z = -2.0 / (far - near)
        let tx = -(right + left) / (right - left)
        let ty = -(top + bottom) / (top - bottom)
        let tz = -(far + near) / (far - near)
        
        return matrix_float4x4(columns: (
            SIMD4<Float>(x, 0, 0, 0),
            SIMD4<Float>(0, y, 0, 0),
            SIMD4<Float>(0, 0, z, 0),
            SIMD4<Float>(tx, ty, tz, 1)
        ))
    }
    
    private func translationMatrix(_ translation: SIMD3<Float>) -> matrix_float4x4 {
        return matrix_float4x4(columns: (
            SIMD4<Float>(1, 0, 0, 0),
            SIMD4<Float>(0, 1, 0, 0),
            SIMD4<Float>(0, 0, 1, 0),
            SIMD4<Float>(translation.x, translation.y, translation.z, 1)
        ))
    }
}

// MARK: - MTKViewDelegate 实现
extension SolarSystemRenderer {
    
    func mtkView(_ view: MTKView, drawableSizeWillChange size: CGSize) {
        updateTransforms()
    }
    
    func draw(in view: MTKView) {
        guard let drawable = view.currentDrawable,
              let renderPassDescriptor = view.currentRenderPassDescriptor,
              let commandBuffer = commandQueue?.makeCommandBuffer(),
              let renderEncoder = commandBuffer.makeRenderCommandEncoder(descriptor: renderPassDescriptor) else {
            return
        }
        
        // 更新变换矩阵
        updateTransforms()
        
        // 全局太阳系位置
        solarSystemGlobalTranslation = translationMatrix(SIMD3<Float>(-0.7, 0.0, 0.0))
        
        // 设置深度状态
        renderEncoder.setDepthStencilState(depthStencilState)
        
        // 渲染3D几何对象（统一管线）
        geometryRenderer?.render(encoder: renderEncoder,
                               transformBuffer: transformBuffer,
                               solarSystemTransform: solarSystemGlobalTranslation,
                               animationTime: animationTime)
        
        // 渲染2D文字（独立管线）
        textRenderer?.render(encoder: renderEncoder,
                           transformBuffer: transformBuffer,
                           animationTime: animationTime,
                           aspectRatio: aspectRatio)
        
        renderEncoder.endEncoding()
        commandBuffer.present(drawable)
        commandBuffer.commit()
    }
}

// MARK: - 辅助函数
private func transformPathPoint(_ point: CGPoint, offset: SIMD2<Float>, scale: Float) -> SIMD2<Float> {
    return SIMD2<Float>(offset.x + Float(point.x) * scale,
                       offset.y + Float(point.y) * scale)
}

private func sampleQuadraticCurve(p0: CGPoint, p1: CGPoint, p2: CGPoint, segments: Int) -> [CGPoint] {
    var points: [CGPoint] = []
    for i in 0...segments {
        let t = CGFloat(i) / CGFloat(segments)
        let x = pow(1 - t, 2) * p0.x + 2 * (1 - t) * t * p1.x + pow(t, 2) * p2.x
        let y = pow(1 - t, 2) * p0.y + 2 * (1 - t) * t * p1.y + pow(t, 2) * p2.y
        points.append(CGPoint(x: x, y: y))
    }
    return points
}

private func sampleCubicCurve(p0: CGPoint, p1: CGPoint, p2: CGPoint, p3: CGPoint, segments: Int) -> [CGPoint] {
    var points: [CGPoint] = []
    for i in 0...segments {
        let t = CGFloat(i) / CGFloat(segments)
        let x = pow(1 - t, 3) * p0.x + 3 * pow(1 - t, 2) * t * p1.x + 3 * (1 - t) * pow(t, 2) * p2.x + pow(t, 3) * p3.x
        let y = pow(1 - t, 3) * p0.y + 3 * pow(1 - t, 2) * t * p1.y + 3 * (1 - t) * pow(t, 2) * p2.y + pow(t, 3) * p3.y
        points.append(CGPoint(x: x, y: y))
    }
    return points
}