//
//  LineAntiAliasingShaders.metal
//  Test2Dlib
//
//  高质量线条抗锯齿着色器集合
//  解决3D线条渲染中的锯齿问题
//

#include <metal_stdlib>
using namespace metal;

// 通用结构体定义
struct VertexIn {
    float3 position [[attribute(0)]];
    float3 color [[attribute(1)]];
};

struct VertexOut {
    float4 position [[position]];
    float3 color;
    float2 uv;
    float lineWidth;
    float2 screenPos;
};

// 抗锯齿线条专用结构体
struct AntiAliasedLineVertex {
    float4 position [[position]];
    float3 color;
    float distance;     // 到线条中心的距离
    float lineWidth;    // 线条宽度
    float2 screenPos;   // 屏幕空间坐标
};

// Uniforms结构体
struct Uniforms {
    float4x4 modelViewProjectionMatrix;
    float4x4 modelMatrix;
    float4x4 viewMatrix;
    float4x4 projectionMatrix;
    float time;
    float lineWidth;
    float2 resolution;
    float pixelScale;
    float antiAliasingWidth;
};

// ================== 改进的线条顶点着色器 ==================
vertex AntiAliasedLineVertex lineVertex(
    VertexIn vertexIn [[stage_in]],
    constant Uniforms& uniforms [[buffer(1)]],
    uint vid [[vertex_id]]
) {
    AntiAliasedLineVertex out;
    
    // 基础变换
    float4 worldPos = uniforms.modelMatrix * float4(vertexIn.position, 1.0);
    float4 viewPos = uniforms.viewMatrix * worldPos;
    out.position = uniforms.projectionMatrix * viewPos;
    
    // 屏幕空间坐标
    out.screenPos = out.position.xy / out.position.w;
    out.screenPos = (out.screenPos + 1.0) * 0.5;
    out.screenPos.y = 1.0 - out.screenPos.y; // 翻转Y轴
    
    // 颜色传递
    out.color = vertexIn.color;
    
    // 线条宽度和距离计算
    out.lineWidth = uniforms.lineWidth * uniforms.pixelScale;
    out.distance = 0.0; // 在几何着色器中计算
    
    return out;
}

// ================== 高质量抗锯齿片段着色器 ==================
fragment float4 antiAliasedLineFragment(
    AntiAliasedLineVertex in [[stage_in]],
    constant Uniforms& uniforms [[buffer(1)]]
) {
    // 计算到线条中心的距离
    float dist = abs(in.distance);
    
    // 线条半宽
    float halfWidth = in.lineWidth * 0.5;
    
    // 抗锯齿边缘宽度（以像素为单位）
    float edgeWidth = uniforms.antiAliasingWidth * uniforms.pixelScale;
    
    // 计算alpha值，使用smooth step实现抗锯齿
    float alpha = 1.0 - smoothstep(halfWidth - edgeWidth, halfWidth + edgeWidth, dist);
    
    // 额外的边缘柔化
    float softEdge = exp(-dist * dist / (halfWidth * halfWidth * 0.5));
    alpha = max(alpha, softEdge * 0.3);
    
    // 应用gamma校正以获得更好的视觉效果
    alpha = pow(alpha, 1.0 / 2.2);
    
    return float4(in.color, alpha);
}

// ================== 轨道线条专用着色器 ==================
vertex VertexOut orbitLineVertex(
    VertexIn vertexIn [[stage_in]],
    constant Uniforms& uniforms [[buffer(1)]],
    uint vid [[vertex_id]]
) {
    VertexOut out;
    
    float4 worldPos = uniforms.modelMatrix * float4(vertexIn.position, 1.0);
    out.position = uniforms.modelViewProjectionMatrix * float4(vertexIn.position, 1.0);
    
    // 计算屏幕空间位置
    float4 screenPos = out.position / out.position.w;
    out.screenPos = screenPos.xy;
    
    out.color = vertexIn.color;
    out.lineWidth = uniforms.lineWidth;
    
    // UV坐标用于纹理映射
    float angle = atan2(vertexIn.position.z, vertexIn.position.x);
    out.uv = float2(angle / (2.0 * M_PI_F), 0.5);
    
    return out;
}

fragment float4 orbitLineFragment(
    VertexOut in [[stage_in]],
    constant Uniforms& uniforms [[buffer(1)]]
) {
    // 计算渐变透明度
    float alpha = 0.6;
    
    // 根据时间添加动画效果
    float wave = sin(uniforms.time * 2.0 + in.uv.x * 10.0) * 0.1 + 0.9;
    alpha *= wave;
    
    // 距离衰减
    float3 worldPos = (uniforms.modelMatrix * float4(in.screenPos, 0.0, 1.0)).xyz;
    float distance = length(worldPos);
    alpha *= 1.0 / (1.0 + distance * 0.01);
    
    return float4(in.color * 1.2, alpha);
}

// ================== 高质量圆形点着色器 ==================
vertex VertexOut pointVertex(
    VertexIn vertexIn [[stage_in]],
    constant Uniforms& uniforms [[buffer(1)]]
) {
    VertexOut out;
    
    out.position = uniforms.modelViewProjectionMatrix * float4(vertexIn.position, 1.0);
    out.color = vertexIn.color;
    out.lineWidth = uniforms.lineWidth * 2.0; // 点比线条粗一些
    
    // 计算点的UV坐标
    out.uv = float2(0.5, 0.5);
    
    return out;
}

fragment float4 pointFragment(
    VertexOut in [[stage_in]],
    constant Uniforms& uniforms [[buffer(1)]]
) {
    // 计算到圆心的距离
    float2 center = float2(0.5, 0.5);
    float dist = distance(in.uv, center);
    
    // 创建圆形
    float radius = 0.4;
    float edgeWidth = 0.05;
    
    float alpha = 1.0 - smoothstep(radius - edgeWidth, radius + edgeWidth, dist);
    
    // 添加内部高光
    float highlight = exp(-dist * dist * 8.0) * 0.3;
    float3 color = in.color + highlight;
    
    return float4(color, alpha);
}

// ================== 多重采样线条着色器 ==================
vertex VertexOut msaaLineVertex(
    VertexIn vertexIn [[stage_in]],
    constant Uniforms& uniforms [[buffer(1)]],
    uint vid [[vertex_id]]
) {
    VertexOut out;
    
    // 基础变换
    out.position = uniforms.modelViewProjectionMatrix * float4(vertexIn.position, 1.0);
    out.color = vertexIn.color;
    out.lineWidth = uniforms.lineWidth;
    
    // 计算线条方向用于宽度扩展
    out.uv = float2(vid % 2, 0); // 简单的UV分配
    
    return out;
}

fragment float4 msaaLineFragment(
    VertexOut in [[stage_in]],
    constant Uniforms& uniforms [[buffer(1)]]
) {
    // 多重采样的简化版本
    float alpha = 1.0;
    
    // 边缘检测和软化
    float edgeDist = abs(in.uv.x - 0.5) * 2.0;
    alpha = 1.0 - smoothstep(0.8, 1.0, edgeDist);
    
    return float4(in.color, alpha);
}

// ================== 距离场线条着色器（最高质量）==================
struct SDFLineVertex {
    float4 position [[position]];
    float3 color;
    float2 uv;
    float lineWidth;
    float2 p0; // 线段起点
    float2 p1; // 线段终点
};

vertex SDFLineVertex sdfLineVertex(
    VertexIn vertexIn [[stage_in]],
    constant Uniforms& uniforms [[buffer(1)]],
    uint vid [[vertex_id]]
) {
    SDFLineVertex out;
    
    out.position = uniforms.modelViewProjectionMatrix * float4(vertexIn.position, 1.0);
    out.color = vertexIn.color;
    out.lineWidth = uniforms.lineWidth;
    
    // 屏幕空间坐标
    float2 screenPos = out.position.xy / out.position.w;
    out.uv = (screenPos + 1.0) * 0.5;
    
    // 简化的线段端点（需要在CPU端提供更多数据）
    out.p0 = float2(0.0, 0.0);
    out.p1 = float2(1.0, 0.0);
    
    return out;
}

// 距离场函数：计算点到线段的距离
float distanceToLine(float2 p, float2 a, float2 b) {
    float2 pa = p - a;
    float2 ba = b - a;
    float h = clamp(dot(pa, ba) / dot(ba, ba), 0.0, 1.0);
    return length(pa - ba * h);
}

fragment float4 sdfLineFragment(
    SDFLineVertex in [[stage_in]],
    constant Uniforms& uniforms [[buffer(1)]]
) {
    // 计算到线条的距离
    float dist = distanceToLine(in.uv, in.p0, in.p1);
    
    // 线条半宽（屏幕空间）
    float halfWidth = in.lineWidth * 0.5 / uniforms.resolution.x;
    
    // 使用距离场计算抗锯齿
    float alpha = 1.0 - smoothstep(halfWidth - 1.0/uniforms.resolution.x, 
                                   halfWidth + 1.0/uniforms.resolution.x, dist);
    
    // 额外的质量提升
    alpha = sqrt(alpha); // gamma校正
    
    return float4(in.color, alpha);
}

// ================== 体积线条着色器（3D厚度感）==================
vertex VertexOut volumetricLineVertex(
    VertexIn vertexIn [[stage_in]],
    constant Uniforms& uniforms [[buffer(1)]],
    uint vid [[vertex_id]]
) {
    VertexOut out;
    
    // 计算世界空间位置
    float4 worldPos = uniforms.modelMatrix * float4(vertexIn.position, 1.0);
    float4 viewPos = uniforms.viewMatrix * worldPos;
    
    // 在视图空间中扩展线条
    float3 normal = normalize(cross(viewPos.xyz, float3(0, 0, 1)));
    float offset = (vid % 2 == 0) ? -uniforms.lineWidth : uniforms.lineWidth;
    viewPos.xyz += normal * offset * 0.001;
    
    out.position = uniforms.projectionMatrix * viewPos;
    out.color = vertexIn.color;
    out.lineWidth = uniforms.lineWidth;
    out.uv = float2(vid % 2, 0);
    
    return out;
}

fragment float4 volumetricLineFragment(
    VertexOut in [[stage_in]],
    constant Uniforms& uniforms [[buffer(1)]]
) {
    // 体积感渲染
    float edge = abs(in.uv.x - 0.5) * 2.0;
    
    // 中心更亮，边缘更暗
    float intensity = 1.0 - edge * edge;
    float3 color = in.color * (0.7 + intensity * 0.5);
    
    // 边缘透明度
    float alpha = 1.0 - smoothstep(0.7, 1.0, edge);
    
    return float4(color, alpha);
}