//
//  Shaders.metal
//  Test2Dlib
//
//  Created by 王雷 on 4/6/25.
//

#include <metal_stdlib>
using namespace metal;

// MARK: - 统一的数据结构

// 统一3D顶点结构
struct UnifiedVertex {
    float3 position [[attribute(0)]];
    float3 normal [[attribute(1)]];
};

// 2D文字顶点结构
struct TextVertex {
    float2 position [[attribute(0)]];
};

struct Transform {
    float4x4 modelMatrix;
    float4x4 viewMatrix;
    float4x4 projectionMatrix;
};

// MARK: - 输出结构

struct UnifiedVertexOut {
    float4 position [[position]];
    float3 worldPosition;
    float3 normal;
};

struct TextVertexOut {
    float4 position [[position]];
};

// MARK: - 统一3D几何着色器

vertex UnifiedVertexOut unified_vertex(UnifiedVertex in [[stage_in]],
                                      constant Transform& transform [[buffer(1)]],
                                      constant float4x4& modelMatrix [[buffer(2)]]) {
    UnifiedVertexOut out;
    
    // 计算完整的变换矩阵
    float4x4 mvpMatrix = transform.projectionMatrix * transform.viewMatrix * modelMatrix;
    
    // 变换顶点位置
    float4 worldPosition = modelMatrix * float4(in.position, 1.0);
    out.position = mvpMatrix * float4(in.position, 1.0);
    out.worldPosition = worldPosition.xyz;
    
    // 变换法向量
    float3x3 normalMatrix = float3x3(modelMatrix.columns[0].xyz,
                                   modelMatrix.columns[1].xyz,
                                   modelMatrix.columns[2].xyz);
    out.normal = normalize(normalMatrix * in.normal);
    
    return out;
}

fragment float4 unified_fragment(UnifiedVertexOut in [[stage_in]],
                                constant float4& color [[buffer(0)]]) {
    return color;
}

// MARK: - 独立2D文字着色器

vertex TextVertexOut text_vertex(TextVertex in [[stage_in]],
                                constant Transform& transform [[buffer(1)]],
                                constant float4x4& modelMatrix [[buffer(2)]]) {
    TextVertexOut out;
    
    // 2D文字直接使用投影变换
    float4x4 mvpMatrix = transform.projectionMatrix * transform.viewMatrix * modelMatrix;
    out.position = mvpMatrix * float4(in.position, 0.0, 1.0);
    
    return out;
}

fragment float4 text_fragment(TextVertexOut in [[stage_in]],
                             constant float4& color [[buffer(0)]]) {
    return color;
}