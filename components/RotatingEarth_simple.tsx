import React, { useRef, useEffect } from 'react';
import * as THREE from 'three';

const RotatingEarth: React.FC = () => {
  const mountRef = useRef<HTMLDivElement>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const animationFrameIdRef = useRef<number | null>(null);

  useEffect(() => {
    if (!mountRef.current) return;

    // Scene setup
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    
    const container = mountRef.current;
    const size = Math.min(container.clientWidth, container.clientHeight);
    renderer.setSize(size, size);
    renderer.setPixelRatio(window.devicePixelRatio);
    container.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // Earth geometry and material
    const geometry = new THREE.SphereGeometry(1, 32, 32);
    const material = new THREE.MeshPhongMaterial({
      color: 0x2233ff,
      shininess: 100,
    });

    // Try to load earth texture
    const textureLoader = new THREE.TextureLoader();
    textureLoader.load(
      'https://cdn.jsdelivr.net/gh/mrdoob/three.js/examples/textures/planets/earth_atmos_2048.jpg',
      (texture) => {
        material.map = texture;
        material.needsUpdate = true;
      },
      undefined,
      (error) => {
        console.warn('Failed to load earth texture, using fallback color');
      }
    );

    const earth = new THREE.Mesh(geometry, material);
    scene.add(earth);

    // Add latitude lines
    const addLatitudeLine = (lat: number, color: number) => {
      const latRad = lat * Math.PI / 180;
      const radius = Math.cos(latRad);
      const y = Math.sin(latRad);
      
      const points = [];
      for (let i = 0; i <= 64; i++) {
        const theta = (i / 64) * 2 * Math.PI;
        points.push(new THREE.Vector3(
          Math.cos(theta) * radius,
          y,
          Math.sin(theta) * radius
        ));
      }
      
      const lineGeometry = new THREE.BufferGeometry().setFromPoints(points);
      const lineMaterial = new THREE.LineBasicMaterial({ color });
      const line = new THREE.Line(lineGeometry, lineMaterial);
      earth.add(line);
    };

    // Add latitude lines
    addLatitudeLine(45, 0xffff00);  // 45°N (yellow)
    addLatitudeLine(0, 0xff0000);   // Equator (red)
    addLatitudeLine(21, 0x00ff00);  // 21°N (green)

    // Add axis line
    const axisGeometry = new THREE.BufferGeometry().setFromPoints([
      new THREE.Vector3(0, -1.3, 0),
      new THREE.Vector3(0, 1.3, 0)
    ]);
    const axisMaterial = new THREE.LineBasicMaterial({ color: 0x3399ff });
    const axisLine = new THREE.Line(axisGeometry, axisMaterial);
    earth.add(axisLine);

    // Lighting
    const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
    scene.add(ambientLight);
    
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(5, 3, 5);
    scene.add(directionalLight);

    // Camera position
    camera.position.z = 3;

    // Animation loop
    const animate = () => {
      earth.rotation.y += 0.01;
      renderer.render(scene, camera);
      animationFrameIdRef.current = requestAnimationFrame(animate);
    };
    animate();

    // Cleanup
    return () => {
      if (animationFrameIdRef.current) {
        cancelAnimationFrame(animationFrameIdRef.current);
      }
      if (rendererRef.current && container.contains(rendererRef.current.domElement)) {
        container.removeChild(rendererRef.current.domElement);
      }
      rendererRef.current?.dispose();
    };
  }, []);

  return (
    <div 
      ref={mountRef} 
      style={{ 
        width: '100%', 
        height: '100%', 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center' 
      }} 
    />
  );
};

export default RotatingEarth;
