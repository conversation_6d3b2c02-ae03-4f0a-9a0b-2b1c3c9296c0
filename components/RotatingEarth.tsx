import React, { useRef, useEffect } from 'react';
import * as THREE from 'three';

const RotatingEarth: React.FC = () => {
  const mountRef = useRef<HTMLDivElement>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const animationFrameIdRef = useRef<number | null>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const earthRef = useRef<THREE.Mesh | null>(null);

  useEffect(() => {
    if (!mountRef.current) return;

    const container = mountRef.current;
    
    // 清理之前的内容
    while (container.firstChild) {
      container.removeChild(container.firstChild);
    }

    // 创建场景
    const scene = new THREE.Scene();
    sceneRef.current = scene;

    // 创建相机
    const camera = new THREE.PerspectiveCamera(
      75, // fov
      1,  // aspect ratio (will be updated)
      0.1, // near
      1000 // far
    );
    camera.position.set(0, 0, 3);

    // 创建渲染器
    const renderer = new THREE.WebGLRenderer({ 
      antialias: true,
      alpha: true 
    });
    renderer.setPixelRatio(window.devicePixelRatio);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    rendererRef.current = renderer;

    // 设置渲染器大小
    const updateSize = () => {
      const size = Math.min(container.clientWidth, container.clientHeight, 400);
      renderer.setSize(size, size);
      camera.aspect = 1;
      camera.updateProjectionMatrix();
    };
    updateSize();

    container.appendChild(renderer.domElement);

    // 创建地球几何体
    const earthGeometry = new THREE.SphereGeometry(1, 64, 64);
    
    // 创建地球材质（先用基础颜色）
    const earthMaterial = new THREE.MeshPhongMaterial({
      color: 0x4488cc,
      shininess: 30,
      transparent: false
    });

    // 创建地球网格
    const earth = new THREE.Mesh(earthGeometry, earthMaterial);
    earth.castShadow = true;
    earth.receiveShadow = true;
    earthRef.current = earth;
    scene.add(earth);

    // 尝试加载地球纹理
    const textureLoader = new THREE.TextureLoader();
    
    // 使用多个备用纹理URL
    const textureUrls = [
      'https://threejs.org/examples/textures/planets/earth_atmos_2048.jpg',
      'https://raw.githubusercontent.com/mrdoob/three.js/dev/examples/textures/planets/earth_atmos_2048.jpg',
      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjU2IiBoZWlnaHQ9IjI1NiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZGVmcz48cmFkaWFsR3JhZGllbnQgaWQ9ImEiPjxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiM0NDg4Y2MiLz48c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiMyMjQ0NjYiLz48L3JhZGlhbEdyYWRpZW50PjwvZGVmcz48cmVjdCB3aWR0aD0iMjU2IiBoZWlnaHQ9IjI1NiIgZmlsbD0idXJsKCNhKSIvPjwvc3ZnPg=='
    ];

    let textureLoaded = false;
    
    const tryLoadTexture = (index: number) => {
      if (index >= textureUrls.length || textureLoaded) return;
      
      textureLoader.load(
        textureUrls[index],
        (texture) => {
          if (!textureLoaded && earthRef.current) {
            textureLoaded = true;
            earthMaterial.map = texture;
            earthMaterial.needsUpdate = true;
            console.log(`地球纹理加载成功: ${textureUrls[index]}`);
          }
        },
        undefined,
        (error) => {
          console.warn(`纹理加载失败 ${index + 1}/${textureUrls.length}: ${textureUrls[index]}`);
          tryLoadTexture(index + 1);
        }
      );
    };
    
    tryLoadTexture(0);

    // 添加纬线
    const addLatitudeLine = (latitude: number, color: number, lineWidth: number = 2) => {
      const latRad = latitude * Math.PI / 180;
      const radius = Math.cos(latRad);
      const y = Math.sin(latRad);
      
      const points = [];
      for (let i = 0; i <= 128; i++) {
        const theta = (i / 128) * 2 * Math.PI;
        points.push(new THREE.Vector3(
          Math.cos(theta) * radius * 1.01, // 稍微大一点避免z-fighting
          y * 1.01,
          Math.sin(theta) * radius * 1.01
        ));
      }
      
      const lineGeometry = new THREE.BufferGeometry().setFromPoints(points);
      const lineMaterial = new THREE.LineBasicMaterial({ 
        color,
        linewidth: lineWidth,
        transparent: true,
        opacity: 0.8
      });
      const line = new THREE.Line(lineGeometry, lineMaterial);
      earth.add(line);
    };

    // 添加重要纬线
    addLatitudeLine(66.5, 0x00ffff, 3);  // 北极圈 (青色)
    addLatitudeLine(45, 0xffff00, 2);    // 45°N (黄色)
    addLatitudeLine(23.5, 0x00ff00, 3);  // 北回归线 (绿色)
    addLatitudeLine(0, 0xff0000, 4);     // 赤道 (红色)
    addLatitudeLine(-23.5, 0x00ff00, 3); // 南回归线 (绿色)
    addLatitudeLine(-45, 0xffff00, 2);   // 45°S (黄色)
    addLatitudeLine(-66.5, 0x00ffff, 3); // 南极圈 (青色)

    // 添加地轴
    const axisGeometry = new THREE.BufferGeometry().setFromPoints([
      new THREE.Vector3(0, -1.5, 0),
      new THREE.Vector3(0, 1.5, 0)
    ]);
    const axisMaterial = new THREE.LineBasicMaterial({ 
      color: 0xffffff,
      linewidth: 3
    });
    const axisLine = new THREE.Line(axisGeometry, axisMaterial);
    scene.add(axisLine);

    // 添加光照
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    scene.add(ambientLight);
    
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1.2);
    directionalLight.position.set(5, 3, 5);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    scene.add(directionalLight);

    // 动画循环
    const animate = () => {
      if (earthRef.current) {
        earthRef.current.rotation.y += 0.005; // 慢速旋转
      }
      
      if (rendererRef.current && sceneRef.current) {
        rendererRef.current.render(sceneRef.current, camera);
      }
      
      animationFrameIdRef.current = requestAnimationFrame(animate);
    };
    
    animate();

    // 窗口大小变化处理
    const handleResize = () => updateSize();
    window.addEventListener('resize', handleResize);

    // 清理函数
    return () => {
      window.removeEventListener('resize', handleResize);
      
      if (animationFrameIdRef.current) {
        cancelAnimationFrame(animationFrameIdRef.current);
      }
      
      if (rendererRef.current) {
        rendererRef.current.dispose();
        if (container.contains(rendererRef.current.domElement)) {
          container.removeChild(rendererRef.current.domElement);
        }
      }
      
      // 清理Three.js资源
      if (sceneRef.current) {
        sceneRef.current.traverse((object) => {
          if (object instanceof THREE.Mesh) {
            object.geometry.dispose();
            if (object.material instanceof THREE.Material) {
              object.material.dispose();
            }
          }
        });
      }
    };
  }, []);

  return (
    <div 
      ref={mountRef} 
      style={{ 
        width: '100%', 
        height: '100%', 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center',
        minHeight: '300px'
      }} 
    />
  );
};

export default RotatingEarth;
