<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/default.metallib</key>
		<data>
		YhnudnkztUZ9KBeic6gCtWfNK/4=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>MacOS/Test2Dlib.debug.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			1tXG4iD5jJkANvmEDBvXNhOnz4U=
			</data>
			<key>requirement</key>
			<string>identifier "Test2Dlib.debug" and anchor apple generic and certificate leaf[subject.CN] = "Apple Development: <EMAIL> (D2VMBSV97Y)" and certificate 1[field.1.2.840.113635.100.6.2.1] /* exists */</string>
		</dict>
		<key>MacOS/__preview.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			i6kvTOJQhgf7FqxHzFAU0jc/EnE=
			</data>
			<key>requirement</key>
			<string>identifier "__preview" and anchor apple generic and certificate leaf[subject.CN] = "Apple Development: <EMAIL> (D2VMBSV97Y)" and certificate 1[field.1.2.840.113635.100.6.2.1] /* exists */</string>
		</dict>
		<key>Resources/default.metallib</key>
		<dict>
			<key>hash2</key>
			<data>
			BJlYXUf8rQMzaCR6l6okLi4IC0jfn8RUhq1nLI/H/wI=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
